<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天</title>
    <link href="css/material-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        .main-content {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding-bottom: 80px; /* 为底部输入区域预留空间 */
            box-sizing: border-box;
        }

        .top-area {
            height: 60%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            padding-bottom: 10px; /* 减少底部padding */
            display: flex;
            flex-direction: column;
            background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 100%);
            min-height: 0; /* 允许flex容器收缩 */
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
        }

        .message.ai {
            justify-content: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 15px;
            position: relative;
        }

        .message.ai .message-content {
            background-color: #e0e0e0;
            color: #333;
        }

        .message.user .message-content {
            background-color: #007bff;
            color: #fff;
        }

        .input-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background-color: #fff;
            border-top: 1px solid #ddd;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            min-height: 50px;
            box-sizing: border-box;
        }

        .input-area {
            display: flex;
            align-items: center;
            background-color: #f0f0f0;
            border-radius: 25px;
            padding: 10px 15px;
            min-height: 50px;
            box-sizing: border-box;
            width: 100%;
            max-width: 100%;
            transition: all 0.3s ease;
        }

        .input-area:focus-within {
            background-color: #e8f4fd;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .input-area input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 5px;
            background: none;
        }

        .input-area button {
            background: none;
            border: none;
            font-size: 24px;
            color: #007bff;
            cursor: pointer;
            margin: 0 5px;
        }

        .input-area button:hover {
            color: #0056b3;
        }

        .voice-input-area {
            user-select: none;
            -webkit-user-select: none;     /* 兼容Safari和旧版浏览器 */
            -webkit-touch-callout: none;   /* 禁用iOS长按菜单 */

            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border-radius: 25px;
            padding: 10px;
            height: 50px;
            box-sizing: border-box;
            flex: 1;
            cursor: pointer;
        }

        .voice-input-area span {
            font-size: 16px;
            color: #007bff;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding-bottom: 90px; /* 移动端增加底部空间 */
            }

            .input-container {
                padding: 12px 15px;
            }

            .input-area {
                min-height: 45px;
                padding: 8px 12px;
            }

            .input-area input {
                font-size: 16px; /* 防止iOS缩放 */
            }

            .input-area button {
                font-size: 20px;
                margin: 0 3px;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding-bottom: 85px;
            }

            .input-container {
                padding: 10px 12px;
            }

            .input-area {
                min-height: 42px;
                padding: 6px 10px;
            }

            .chat-container {
                padding: 15px;
            }
        }

        /* 确保聊天容器在有键盘时的处理 */
        @media (max-height: 600px) {
            .top-area {
                height: 40%;
            }

            .main-content {
                padding-bottom: 75px;
            }
        }
    </style>
</head>
<body>
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 上半区域 -->
        <div class="top-area"></div>

        <!-- 聊天容器 -->
        <div class="chat-container" id="chat-container">
            <!-- 聊天记录将显示在这里 -->
        </div>
    </div>

    <!-- 输入区域 - 固定在底部 -->
    <div class="input-container">
        <div class="input-area" id="input-area">
            <button class="input-toggle" id="toggle-button">
                <i class="material-icons">keyboard</i>
            </button>
            <div class="voice-input-area" id="voice-input-area">
                <span>点击开始对话</span>
            </div>
            <input type="text" id="text-input" placeholder="发送消息" style="display: none;">
            <button class="send-button" id="send-button" style="display: none;">
                <i class="material-icons">send</i>
            </button>
        </div>
    </div>
    <script src="js/audio_recorder.js"></script>
    <script src="js/dialog_realtime.js"></script>
</body>
</html>