<!doctype html>
<html lang="en-us">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>MiniLive</title>
        <style>
            body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                background-color: #f0f0f0;
                overflow: hidden; /* 防止滚动条出现 */
            }

            video, canvas {
                border: 2px solid #ccc;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute; /* Position the iframe absolutely */
                top: 0;
                left: 0;
                border: none;
                z-index: 5; /* Ensure iframe is above other elements */
            }

            /* 居中显示"请点击开始" */
            #startMessage {
                position: absolute;
                top: 60%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 24px;
                font-weight: bold;
                color: #333;
                z-index: -2;
            }
            #dropdownContainer, #voiceDropdownContainer, #backgroundDropdownContainer {
                position: absolute;
                top: 20px;
                left: 20px;
                z-index: 10;
            }

            #voiceDropdownContainer {
                top: 70px;
            }

            #backgroundDropdownContainer {
                top: 120px;
            }

            #characterDropdown, #voiceDropdown, #backgroundDropdown {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                padding: 10px 40px 10px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #333;
                background-color: #fff;
                border: 2px solid #ccc;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                cursor: pointer;
                outline: none;
                transition: all 0.3s ease;
            }

            #characterDropdown:hover, #voiceDropdown:hover, #backgroundDropdown:hover {
                border-color: #888;
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            }

            #characterDropdown:focus, #voiceDropdown:focus, #backgroundDropdown:focus {
                border-color: #555;
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
            }

            #dropdownContainer::after, #voiceDropdownContainer::after, #backgroundDropdownContainer::after {
                content: '▼';
                position: absolute;
                top: 50%;
                right: 15px;
                transform: translateY(-50%);
                pointer-events: none;
                color: #666;
                font-size: 12px;
            }
            #canvasEl {
                position: absolute;
                left: -9999px;  /* 移出可视区域 */
                top: -9999px;
                /* 保持canvas正常尺寸 */
                width: 300px;
                height: 150px;
            }

            /* 背景上传控件样式 */
            #backgroundUploadContainer {
                position: absolute;
                top: 170px;
                left: 20px;
                z-index: 10;
            }

            #backgroundUploadBtn {
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                color: #fff;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            }

            #backgroundUploadBtn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            #backgroundFileInput {
                display: none;
            }

            /* 背景预览样式 */
            #backgroundPreviewContainer {
                position: absolute;
                top: 220px;
                left: 20px;
                z-index: 10;
                display: none;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 8px;
                padding: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                max-width: 200px;
            }

            #backgroundPreview {
                width: 100%;
                max-width: 180px;
                height: auto;
                border-radius: 4px;
                margin-bottom: 8px;
            }

            .preview-buttons {
                display: flex;
                gap: 8px;
            }

            .preview-btn {
                flex: 1;
                padding: 6px 12px;
                font-size: 12px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .preview-btn.apply {
                background: #4CAF50;
                color: white;
            }

            .preview-btn.cancel {
                background: #f44336;
                color: white;
            }

            .preview-btn:hover {
                opacity: 0.8;
            }

            /* 加载状态样式 */
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* 错误提示样式 */
            .error-message {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #f44336;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 1001;
                display: none;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
        </style>
    </head>
    <body>
        <div id="dropdownContainer">
            <select id="characterDropdown">
                <option value="assets">女性一</option>
                <option value="assets1">女性一</option>
                <option value="assets2">女性一</option>
                <option value="assets4">女性一</option>
                <option value="assets5">女性一</option>
                <option value="assets7">女性一</option>
                <option value="assets8">女性一</option>
                <option value="assets11">女性一</option>
                <option value="assets12">女性一</option>
                <option value="assets13">女性一</option>
                <option value="assets14">女性一</option>
            </select>
        </div>
        <div id="voiceDropdownContainer">
            <select id="voiceDropdown">
                <option value=0>温柔女</option>
                <option value=1>温柔男</option>
                <option value=2>甜美女</option>
                <option value=3>青年女</option>
                <option value=4>磁性男</option>
            </select>
        </div>
        <div id="backgroundDropdownContainer">
            <select id="backgroundDropdown">
                <option value="default">默认背景</option>
                <option value="office">办公室</option>
                <option value="studio">演播室</option>
                <option value="living_room">客厅</option>
                <option value="conference">会议室</option>
                <option value="nature">自然风景</option>
                <option value="gradient_blue">蓝色渐变</option>
                <option value="gradient_purple">紫色渐变</option>
                <option value="gradient_green">绿色渐变</option>
            </select>
        </div>
        <div id="backgroundUploadContainer">
            <button id="backgroundUploadBtn">上传背景</button>
            <input type="file" id="backgroundFileInput" accept="image/*">
        </div>
        <div id="backgroundPreviewContainer">
            <img id="backgroundPreview" alt="背景预览">
            <div class="preview-buttons">
                <button class="preview-btn apply" id="applyBackgroundBtn">应用</button>
                <button class="preview-btn cancel" id="cancelBackgroundBtn">取消</button>
            </div>
        </div>

        <figure style="overflow:visible;" id="loadingSpinner">
            <strong>MiniMates: loading...</strong>
        </figure>
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>
        <div id="startMessage">加载中</div>

        <!-- 加载状态覆盖层 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
        </div>

        <!-- 错误提示 -->
        <div class="error-message" id="errorMessage"></div>
        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
    </body>
</html>