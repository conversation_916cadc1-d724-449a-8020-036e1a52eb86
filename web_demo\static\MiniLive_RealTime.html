<!doctype html>
<html lang="zh-CN">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>🎭 数字人直播系统 - MiniLive</title>
        <!-- 引入现代字体 -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <!-- 引入图标字体 -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            /* CSS变量定义 */
            :root {
                --primary-color: #6366f1;
                --primary-dark: #4f46e5;
                --secondary-color: #8b5cf6;
                --accent-color: #06b6d4;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --error-color: #ef4444;
                --text-primary: #1f2937;
                --text-secondary: #6b7280;
                --text-light: #9ca3af;
                --bg-primary: #ffffff;
                --bg-secondary: #f9fafb;
                --bg-tertiary: #f3f4f6;
                --border-color: #e5e7eb;
                --border-light: #f3f4f6;
                --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
                --spacing-xs: 0.25rem;
                --spacing-sm: 0.5rem;
                --spacing-md: 1rem;
                --spacing-lg: 1.5rem;
                --spacing-xl: 2rem;
            }

            /* 全局样式重置 */
            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;

                /* 当前使用：深蓝色专业背景 */
                background: #1e293b;

                /* 备选方案1: 深灰色现代背景 */
                /* background: #374151; */

                /* 备选方案2: 纯白色简洁背景 */
                /* background: #ffffff; */

                /* 备选方案3: 静态蓝紫渐变背景 */
                /* background: linear-gradient(135deg, #1e293b 0%, #334155 100%); */

                /* 备选方案4: 静态深色渐变背景 */
                /* background: linear-gradient(135deg, #111827 0%, #374151 100%); */

                /* 备选方案5: 静态蓝灰渐变背景 */
                /* background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%); */

                /* 备选方案6: 静态紫色渐变背景 */
                /* background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%); */

                overflow: hidden;
                color: var(--text-primary);
                line-height: 1.6;
            }

            /* Canvas样式 */
            video, canvas {
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow-xl);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
                border-radius: var(--radius-xl);
                box-shadow:
                    0 25px 50px -12px rgba(0, 0, 0, 0.25),
                    0 0 0 1px rgba(255, 255, 255, 0.1);
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: none;
                z-index: 5;
                border-radius: var(--radius-xl);
            }

            /* 加载消息样式 */
            #startMessage {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 1.5rem;
                font-weight: 600;
                color: #ffffff;
                z-index: 10;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                padding: var(--spacing-lg) var(--spacing-xl);
                border-radius: var(--radius-xl);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: var(--shadow-xl);
                text-align: center;
                animation: pulse 2s ease-in-out infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.02); }
            }
            /* 控制面板容器 */
            .control-panel {
                position: absolute;
                top: var(--spacing-lg);
                left: var(--spacing-lg);
                z-index: 100;
                display: flex;
                flex-direction: column;
                gap: var(--spacing-md);
                min-width: 320px;
                max-width: 380px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl);
                padding: var(--spacing-xl);
                box-shadow:
                    var(--shadow-xl),
                    0 0 0 1px rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                animation: slideInLeft 0.6s ease-out;
            }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-100px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .control-panel:hover {
                transform: translateY(-2px);
                box-shadow:
                    0 25px 50px -12px rgba(0, 0, 0, 0.25),
                    0 0 0 1px rgba(255, 255, 255, 0.1);
            }

            /* 面板标题 */
            .panel-title {
                font-size: 1.25rem;
                font-weight: 700;
                color: var(--text-primary);
                margin-bottom: var(--spacing-md);
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
            }

            .panel-title i {
                color: var(--primary-color);
                font-size: 1.5rem;
            }

            /* 控制组 */
            .control-group {
                display: flex;
                flex-direction: column;
                gap: var(--spacing-sm);
                margin-bottom: var(--spacing-md);
            }

            .control-group:last-child {
                margin-bottom: 0;
            }

            .control-label {
                font-size: 0.875rem;
                font-weight: 600;
                color: var(--text-secondary);
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin-bottom: var(--spacing-xs);
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
            }

            .control-label i {
                color: var(--primary-color);
                font-size: 0.875rem;
            }

            /* 下拉菜单容器 */
            .dropdown-container {
                position: relative;
            }

            /* 下拉菜单样式 */
            .dropdown-select {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                width: 100%;
                padding: 0.875rem 3rem 0.875rem 1rem;
                font-size: 0.875rem;
                font-weight: 500;
                color: var(--text-primary);
                background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
                border: 2px solid var(--border-light);
                border-radius: var(--radius-lg);
                cursor: pointer;
                outline: none;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-sm);
                position: relative;
            }

            .dropdown-select:hover {
                border-color: var(--primary-color);
                box-shadow: var(--shadow-md);
                transform: translateY(-1px);
                background: var(--bg-primary);
            }

            .dropdown-select:focus {
                border-color: var(--primary-color);
                box-shadow:
                    var(--shadow-md),
                    0 0 0 3px rgba(99, 102, 241, 0.1);
                transform: translateY(-1px);
            }

            /* 下拉箭头 */
            .dropdown-container::after {
                content: '';
                position: absolute;
                top: 50%;
                right: 1rem;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 6px solid var(--text-secondary);
                pointer-events: none;
                transition: all 0.3s ease;
            }

            .dropdown-select:focus + .dropdown-container::after,
            .dropdown-container:hover::after {
                border-top-color: var(--primary-color);
                transform: translateY(-50%) rotate(180deg);
            }





            #canvasEl {
                position: absolute;
                left: -9999px;
                top: -9999px;
                width: 300px;
                height: 150px;
            }

            /* 响应式设计 */
            @media (max-width: 1024px) {
                .control-panel {
                    min-width: 280px;
                    max-width: 320px;
                }
            }

            @media (max-width: 768px) {
                .control-panel {
                    position: fixed;
                    top: var(--spacing-sm);
                    left: var(--spacing-sm);
                    right: var(--spacing-sm);
                    max-width: none;
                    min-width: auto;
                    padding: var(--spacing-lg);
                }

                #startMessage {
                    font-size: 1.25rem;
                    padding: var(--spacing-md) var(--spacing-lg);
                }
            }

            @media (max-width: 480px) {
                .control-panel {
                    padding: var(--spacing-md);
                    gap: var(--spacing-sm);
                }

                .dropdown-select {
                    padding: 0.75rem 2.5rem 0.75rem 0.875rem;
                    font-size: 0.8125rem;
                }

                .panel-title {
                    font-size: 1.125rem;
                }

                #startMessage {
                    font-size: 1.125rem;
                    padding: var(--spacing-sm) var(--spacing-md);
                }
            }

            /* 加载动画 */
            #loadingSpinner {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 20;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                padding: var(--spacing-xl);
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow-xl);
                border: 1px solid rgba(255, 255, 255, 0.2);
                text-align: center;
                animation: pulse 2s ease-in-out infinite;
            }

            #loadingSpinner strong {
                color: var(--primary-color);
                font-weight: 600;
                font-size: 1.125rem;
            }
        </style>
    </head>
    <body>
        <!-- 美化的控制面板 -->
        <div class="control-panel">
            <!-- 面板标题 -->
            <div class="panel-title">
                <i class="fas fa-robot"></i>
                数字人直播控制台
            </div>

            <!-- 角色选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-user"></i>
                    数字人角色
                </div>
                <div class="dropdown-container">
                    <select id="characterDropdown" class="dropdown-select">
                        <option value="assets">🧑‍💼 商务女性</option>
                        <option value="assets1">👩‍🎓 知性女性</option>
                        <option value="assets2">👩‍💻 科技女性</option>
                        <option value="assets4">👩‍🏫 教师女性</option>
                        <option value="assets5">👩‍⚕️ 医护女性</option>
                        <option value="assets7">👩‍🎨 艺术女性</option>
                        <option value="assets8">👩‍🔬 研究女性</option>
                        <option value="assets11">👩‍💼 职场女性</option>
                        <option value="assets12">👩‍🎤 主播女性</option>
                        <option value="assets13">👩‍🏭 工程女性</option>
                        <option value="assets14">👩‍🚀 未来女性</option>
                    </select>
                </div>
            </div>

            <!-- 语音选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-microphone"></i>
                    语音类型
                </div>
                <div class="dropdown-container">
                    <select id="voiceDropdown" class="dropdown-select">
                        <option value="0">🎵 温柔女声</option>
                        <option value="1">🎙️ 温柔男声</option>
                        <option value="2">🌸 甜美女声</option>
                        <option value="3">✨ 青年女声</option>
                        <option value="4">🎯 磁性男声</option>
                    </select>
                </div>
            </div>


        </div>



        <!-- 系统加载提示 -->
        <figure id="loadingSpinner">
            <div style="text-align: center;">
                <i class="fas fa-robot" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem; display: block;"></i>
                <strong style="color: var(--primary-color);">🚀 数字人系统启动中...</strong>
                <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.5rem;">
                    正在加载AI模型和渲染引擎
                </div>
            </div>
        </figure>

        <!-- Canvas元素 -->
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>

        <!-- 启动消息 -->
        <!-- <div id="startMessage">
            <div style="text-align: center;">
                <i class="fas fa-play-circle" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                <div style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem;">系统已就绪</div>
                <div style="font-size: 0.875rem; opacity: 0.9;">点击开始体验数字人直播</div>
            </div>
        </div> -->
        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
    </body>
</html>