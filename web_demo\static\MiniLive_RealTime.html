<!doctype html>
<html lang="zh-CN">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>🎭 数字人直播系统 - MiniLive</title>
        <!-- 引入现代字体 -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <!-- 引入图标字体 -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            /* CSS变量定义 */
            :root {
                --primary-color: #6366f1;
                --primary-dark: #4f46e5;
                --secondary-color: #8b5cf6;
                --accent-color: #06b6d4;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --error-color: #ef4444;
                --text-primary: #1f2937;
                --text-secondary: #6b7280;
                --text-light: #9ca3af;
                --bg-primary: #ffffff;
                --bg-secondary: #f9fafb;
                --bg-tertiary: #f3f4f6;
                --border-color: #e5e7eb;
                --border-light: #f3f4f6;
                --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
                --spacing-xs: 0.25rem;
                --spacing-sm: 0.5rem;
                --spacing-md: 1rem;
                --spacing-lg: 1.5rem;
                --spacing-xl: 2rem;
            }

            /* 全局样式重置 */
            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                overflow: hidden;
                color: var(--text-primary);
                line-height: 1.6;
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            /* Canvas样式 */
            video, canvas {
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow-xl);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
                border-radius: var(--radius-xl);
                box-shadow:
                    0 25px 50px -12px rgba(0, 0, 0, 0.25),
                    0 0 0 1px rgba(255, 255, 255, 0.1);
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: none;
                z-index: 5;
                border-radius: var(--radius-xl);
            }

            /* 加载消息样式 */
            #startMessage {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--bg-primary);
                z-index: 10;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                padding: var(--spacing-lg) var(--spacing-xl);
                border-radius: var(--radius-xl);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: var(--shadow-xl);
                text-align: center;
                animation: pulse 2s ease-in-out infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.02); }
            }
            /* 控制面板容器 */
            .control-panel {
                position: absolute;
                top: var(--spacing-lg);
                left: var(--spacing-lg);
                z-index: 100;
                display: flex;
                flex-direction: column;
                gap: var(--spacing-md);
                min-width: 320px;
                max-width: 380px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl);
                padding: var(--spacing-xl);
                box-shadow:
                    var(--shadow-xl),
                    0 0 0 1px rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                animation: slideInLeft 0.6s ease-out;
            }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-100px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .control-panel:hover {
                transform: translateY(-2px);
                box-shadow:
                    0 25px 50px -12px rgba(0, 0, 0, 0.25),
                    0 0 0 1px rgba(255, 255, 255, 0.1);
            }

            /* 面板标题 */
            .panel-title {
                font-size: 1.25rem;
                font-weight: 700;
                color: var(--text-primary);
                margin-bottom: var(--spacing-md);
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
            }

            .panel-title i {
                color: var(--primary-color);
                font-size: 1.5rem;
            }

            /* 控制组 */
            .control-group {
                display: flex;
                flex-direction: column;
                gap: var(--spacing-sm);
                margin-bottom: var(--spacing-md);
            }

            .control-group:last-child {
                margin-bottom: 0;
            }

            .control-label {
                font-size: 0.875rem;
                font-weight: 600;
                color: var(--text-secondary);
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin-bottom: var(--spacing-xs);
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
            }

            .control-label i {
                color: var(--primary-color);
                font-size: 0.875rem;
            }

            /* 下拉菜单容器 */
            .dropdown-container {
                position: relative;
            }

            /* 下拉菜单样式 */
            .dropdown-select {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                width: 100%;
                padding: 0.875rem 3rem 0.875rem 1rem;
                font-size: 0.875rem;
                font-weight: 500;
                color: var(--text-primary);
                background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
                border: 2px solid var(--border-light);
                border-radius: var(--radius-lg);
                cursor: pointer;
                outline: none;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-sm);
                position: relative;
            }

            .dropdown-select:hover {
                border-color: var(--primary-color);
                box-shadow: var(--shadow-md);
                transform: translateY(-1px);
                background: var(--bg-primary);
            }

            .dropdown-select:focus {
                border-color: var(--primary-color);
                box-shadow:
                    var(--shadow-md),
                    0 0 0 3px rgba(99, 102, 241, 0.1);
                transform: translateY(-1px);
            }

            /* 下拉箭头 */
            .dropdown-container::after {
                content: '';
                position: absolute;
                top: 50%;
                right: 1rem;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 6px solid var(--text-secondary);
                pointer-events: none;
                transition: all 0.3s ease;
            }

            .dropdown-select:focus + .dropdown-container::after,
            .dropdown-container:hover::after {
                border-top-color: var(--primary-color);
                transform: translateY(-50%) rotate(180deg);
            }

            /* 背景控制区域 */
            .background-controls {
                border-top: 2px solid var(--border-light);
                padding-top: var(--spacing-md);
                margin-top: var(--spacing-md);
            }

            /* 颜色选择器容器 */
            .color-picker-container {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                margin-top: var(--spacing-sm);
                padding: var(--spacing-sm);
                background: var(--bg-secondary);
                border-radius: var(--radius-md);
                border: 1px solid var(--border-light);
            }

            .color-picker {
                width: 3rem;
                height: 2.5rem;
                border: 2px solid var(--border-color);
                border-radius: var(--radius-md);
                cursor: pointer;
                outline: none;
                transition: all 0.3s ease;
                background: none;
                padding: 0;
                overflow: hidden;
            }

            .color-picker:hover {
                border-color: var(--primary-color);
                transform: scale(1.05);
                box-shadow: var(--shadow-md);
            }

            .color-picker:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            }

            /* 预设颜色网格 */
            .color-presets {
                display: grid;
                grid-template-columns: repeat(6, 1fr);
                gap: var(--spacing-xs);
                margin-top: var(--spacing-sm);
                padding: var(--spacing-sm);
                background: var(--bg-secondary);
                border-radius: var(--radius-md);
                border: 1px solid var(--border-light);
            }

            .color-preset {
                width: 2.5rem;
                height: 2.5rem;
                border: 2px solid var(--border-color);
                border-radius: var(--radius-md);
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            .color-preset::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: inherit;
                transition: all 0.3s ease;
            }

            .color-preset:hover {
                border-color: var(--primary-color);
                transform: scale(1.1);
                box-shadow: var(--shadow-lg);
                z-index: 10;
            }

            .color-preset.active {
                border-color: var(--primary-color);
                box-shadow:
                    var(--shadow-md),
                    0 0 0 3px rgba(99, 102, 241, 0.2);
                transform: scale(1.05);
            }

            .color-preset::after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-size: 0.875rem;
                font-weight: 700;
                opacity: 0;
                transition: all 0.3s ease;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
                z-index: 2;
            }

            .color-preset.active::after {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1.2);
            }

            /* 上传按钮容器 */
            .upload-container {
                margin-top: var(--spacing-md);
            }

            .upload-btn {
                width: 100%;
                padding: 0.875rem 1rem;
                font-size: 0.875rem;
                font-weight: 600;
                color: white;
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
                border: none;
                border-radius: var(--radius-lg);
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: var(--spacing-sm);
                box-shadow: var(--shadow-md);
            }

            .upload-btn:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
                background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-color) 100%);
            }

            .upload-btn:active {
                transform: translateY(0);
                box-shadow: var(--shadow-sm);
            }

            .upload-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s ease;
            }

            .upload-btn:hover::before {
                left: 100%;
            }

            .file-input {
                display: none;
            }

            /* 状态指示器 */
            .status-indicator {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                font-size: 0.75rem;
                color: var(--text-secondary);
                margin-top: var(--spacing-md);
                padding: var(--spacing-sm) var(--spacing-md);
                background: var(--bg-secondary);
                border-radius: var(--radius-md);
                border: 1px solid var(--border-light);
            }

            .status-dot {
                width: 0.75rem;
                height: 0.75rem;
                border-radius: 50%;
                background: var(--success-color);
                animation: statusPulse 2s ease-in-out infinite;
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
            }

            @keyframes statusPulse {
                0% {
                    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
                }
                50% {
                    box-shadow: 0 0 0 8px rgba(16, 185, 129, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
                }
            }

            .status-dot.loading {
                background: var(--warning-color);
                animation: statusSpin 1s linear infinite;
                box-shadow: none;
            }

            .status-dot.error {
                background: var(--error-color);
                animation: none;
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
            }

            @keyframes statusSpin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* 预览容器 */
            .preview-container {
                position: absolute;
                top: var(--spacing-lg);
                right: var(--spacing-lg);
                z-index: 100;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl);
                padding: var(--spacing-lg);
                box-shadow: var(--shadow-xl);
                border: 1px solid rgba(255, 255, 255, 0.2);
                display: none;
                max-width: 280px;
                animation: slideInRight 0.4s ease-out;
            }

            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .preview-image {
                width: 100%;
                height: auto;
                border-radius: var(--radius-lg);
                margin-bottom: var(--spacing-md);
                box-shadow: var(--shadow-md);
                transition: all 0.3s ease;
            }

            .preview-image:hover {
                transform: scale(1.02);
                box-shadow: var(--shadow-lg);
            }

            .preview-buttons {
                display: flex;
                gap: var(--spacing-sm);
            }

            .preview-btn {
                flex: 1;
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: 0.75rem;
                font-weight: 600;
                border: none;
                border-radius: var(--radius-md);
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: var(--spacing-xs);
            }

            .preview-btn.apply {
                background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
                color: white;
            }

            .preview-btn.cancel {
                background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
                color: white;
            }

            .preview-btn:hover {
                transform: translateY(-1px);
                box-shadow: var(--shadow-md);
            }

            .preview-btn:active {
                transform: translateY(0);
            }

            /* 加载和错误提示 */
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            .loading-spinner {
                width: 3rem;
                height: 3rem;
                border: 4px solid rgba(255, 255, 255, 0.3);
                border-top: 4px solid var(--primary-color);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .error-message {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
                color: white;
                padding: var(--spacing-lg) var(--spacing-xl);
                border-radius: var(--radius-lg);
                font-size: 0.875rem;
                font-weight: 500;
                z-index: 1001;
                display: none;
                box-shadow: var(--shadow-xl);
                max-width: 320px;
                text-align: center;
                animation: slideInUp 0.4s ease-out;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translate(-50%, -30px);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%);
                }
            }

            /* 拖拽提示 */
            .drag-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(99, 102, 241, 0.1);
                backdrop-filter: blur(4px);
                border: 3px dashed var(--primary-color);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 999;
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--primary-color);
                animation: dragPulse 1s ease-in-out infinite;
            }

            @keyframes dragPulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.7; }
            }

            #canvasEl {
                position: absolute;
                left: -9999px;
                top: -9999px;
                width: 300px;
                height: 150px;
            }

            /* 响应式设计 */
            @media (max-width: 1024px) {
                .control-panel {
                    min-width: 280px;
                    max-width: 320px;
                }

                .preview-container {
                    max-width: 240px;
                }
            }

            @media (max-width: 768px) {
                .control-panel {
                    position: fixed;
                    top: var(--spacing-sm);
                    left: var(--spacing-sm);
                    right: var(--spacing-sm);
                    max-width: none;
                    min-width: auto;
                    padding: var(--spacing-lg);
                }

                .preview-container {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    right: auto;
                    max-width: 300px;
                    width: calc(100vw - 2rem);
                }

                .color-presets {
                    grid-template-columns: repeat(4, 1fr);
                }

                #startMessage {
                    font-size: 1.25rem;
                    padding: var(--spacing-md) var(--spacing-lg);
                }
            }

            @media (max-width: 480px) {
                .control-panel {
                    padding: var(--spacing-md);
                    gap: var(--spacing-sm);
                }

                .dropdown-select {
                    padding: 0.75rem 2.5rem 0.75rem 0.875rem;
                    font-size: 0.8125rem;
                }

                .color-preset {
                    width: 2rem;
                    height: 2rem;
                }

                .color-presets {
                    grid-template-columns: repeat(3, 1fr);
                }

                .panel-title {
                    font-size: 1.125rem;
                }

                #startMessage {
                    font-size: 1.125rem;
                    padding: var(--spacing-sm) var(--spacing-md);
                }
            }

            /* 加载动画 */
            #loadingSpinner {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 20;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                padding: var(--spacing-xl);
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow-xl);
                border: 1px solid rgba(255, 255, 255, 0.2);
                text-align: center;
                animation: pulse 2s ease-in-out infinite;
            }

            #loadingSpinner strong {
                color: var(--primary-color);
                font-weight: 600;
                font-size: 1.125rem;
            }
        </style>
    </head>
    <body>
        <!-- 美化的控制面板 -->
        <div class="control-panel">
            <!-- 面板标题 -->
            <div class="panel-title">
                <i class="fas fa-robot"></i>
                数字人直播控制台
            </div>

            <!-- 角色选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-user"></i>
                    数字人角色
                </div>
                <div class="dropdown-container">
                    <select id="characterDropdown" class="dropdown-select">
                        <option value="assets">🧑‍💼 商务女性</option>
                        <option value="assets1">👩‍🎓 知性女性</option>
                        <option value="assets2">👩‍💻 科技女性</option>
                        <option value="assets4">👩‍🏫 教师女性</option>
                        <option value="assets5">👩‍⚕️ 医护女性</option>
                        <option value="assets7">👩‍🎨 艺术女性</option>
                        <option value="assets8">👩‍🔬 研究女性</option>
                        <option value="assets11">👩‍💼 职场女性</option>
                        <option value="assets12">👩‍🎤 主播女性</option>
                        <option value="assets13">👩‍🏭 工程女性</option>
                        <option value="assets14">👩‍🚀 未来女性</option>
                    </select>
                </div>
            </div>

            <!-- 语音选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-microphone"></i>
                    语音类型
                </div>
                <div class="dropdown-container">
                    <select id="voiceDropdown" class="dropdown-select">
                        <option value="0">🎵 温柔女声</option>
                        <option value="1">🎙️ 温柔男声</option>
                        <option value="2">🌸 甜美女声</option>
                        <option value="3">✨ 青年女声</option>
                        <option value="4">🎯 磁性男声</option>
                    </select>
                </div>
            </div>

            <!-- 背景设置 -->
            <div class="control-group background-controls">
                <div class="control-label">
                    <i class="fas fa-image"></i>
                    背景设置
                </div>

                <!-- 背景类型选择 -->
                <div class="dropdown-container">
                    <select id="backgroundDropdown" class="dropdown-select">
                        <option value="default">🎨 默认背景</option>
                        <option value="office">🏢 现代办公室</option>
                        <option value="studio">🎬 专业演播室</option>
                        <option value="living_room">🏠 温馨客厅</option>
                        <option value="conference">💼 商务会议室</option>
                        <option value="nature">🌿 自然风景</option>
                        <option value="gradient_blue">🌊 蓝色渐变</option>
                        <option value="gradient_purple">🌌 紫色渐变</option>
                        <option value="gradient_green">🍃 绿色渐变</option>
                        <option value="custom_color">🎨 自定义颜色</option>
                    </select>
                </div>

                <!-- 颜色选择器 -->
                <div class="color-picker-container" id="colorPickerContainer" style="display: none;">
                    <input type="color" id="colorPicker" class="color-picker" value="#f0f0f0">
                    <span style="font-size: 0.75rem; color: var(--text-secondary); font-weight: 500;">
                        <i class="fas fa-palette"></i> 选择自定义颜色
                    </span>
                </div>

                <!-- 预设颜色 -->
                <div class="color-presets" id="colorPresets" style="display: none;">
                    <div class="color-preset" data-color="#ffffff" style="background: #ffffff;" title="纯白色"></div>
                    <div class="color-preset" data-color="#000000" style="background: #000000;" title="纯黑色"></div>
                    <div class="color-preset" data-color="#3b82f6" style="background: #3b82f6;" title="天空蓝"></div>
                    <div class="color-preset" data-color="#10b981" style="background: #10b981;" title="翡翠绿"></div>
                    <div class="color-preset" data-color="#ef4444" style="background: #ef4444;" title="珊瑚红"></div>
                    <div class="color-preset" data-color="#f59e0b" style="background: #f59e0b;" title="琥珀黄"></div>
                    <div class="color-preset" data-color="#8b5cf6" style="background: #8b5cf6;" title="紫罗兰"></div>
                    <div class="color-preset" data-color="#f97316" style="background: #f97316;" title="橘子橙"></div>
                    <div class="color-preset" data-color="#06b6d4" style="background: #06b6d4;" title="青瓷蓝"></div>
                    <div class="color-preset" data-color="#ec4899" style="background: #ec4899;" title="玫瑰粉"></div>
                    <div class="color-preset" data-color="#6b7280" style="background: #6b7280;" title="石墨灰"></div>
                    <div class="color-preset" data-color="#f3f4f6" style="background: #f3f4f6;" title="云雾白"></div>
                </div>

                <!-- 上传按钮 -->
                <div class="upload-container">
                    <button id="backgroundUploadBtn" class="upload-btn">
                        <i class="fas fa-cloud-upload-alt"></i>
                        上传自定义背景
                    </button>
                    <input type="file" id="backgroundFileInput" class="file-input" accept="image/*">
                </div>

                <!-- 状态指示器 -->
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">
                        <i class="fas fa-check-circle"></i>
                        背景系统就绪
                    </span>
                </div>
            </div>
        </div>

        <!-- 背景预览容器 -->
        <div class="preview-container" id="backgroundPreviewContainer">
            <img id="backgroundPreview" class="preview-image" alt="背景预览">
            <div class="preview-buttons">
                <button class="preview-btn apply" id="applyBackgroundBtn">
                    <i class="fas fa-check"></i>
                    应用
                </button>
                <button class="preview-btn cancel" id="cancelBackgroundBtn">
                    <i class="fas fa-times"></i>
                    取消
                </button>
            </div>
        </div>

        <!-- 拖拽提示覆盖层 -->
        <div class="drag-overlay" id="dragOverlay">
            <div style="text-align: center;">
                <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                <div style="font-size: 1.5rem; font-weight: 600;">拖拽图片到此处</div>
                <div style="font-size: 1rem; opacity: 0.8; margin-top: 0.5rem;">支持 JPG、PNG、GIF、WebP 格式</div>
            </div>
        </div>

        <!-- 加载状态覆盖层 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div style="text-align: center; color: white;">
                <div class="loading-spinner" style="margin: 0 auto 1rem;"></div>
                <div style="font-size: 1rem; font-weight: 500;">正在处理中...</div>
            </div>
        </div>

        <!-- 错误提示 -->
        <div class="error-message" id="errorMessage">
            <i class="fas fa-exclamation-triangle" style="margin-right: 0.5rem;"></i>
            <span id="errorText">发生错误</span>
        </div>

        <!-- 系统加载提示 -->
        <figure id="loadingSpinner">
            <div style="text-align: center;">
                <i class="fas fa-robot" style="font-size: 2rem; color: var(--primary-color); margin-bottom: 1rem; display: block;"></i>
                <strong style="color: var(--primary-color);">🚀 数字人系统启动中...</strong>
                <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.5rem;">
                    正在加载AI模型和渲染引擎
                </div>
            </div>
        </figure>

        <!-- Canvas元素 -->
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>

        <!-- 启动消息 -->
        <div id="startMessage">
            <div style="text-align: center;">
                <i class="fas fa-play-circle" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                <div style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem;">系统已就绪</div>
                <div style="font-size: 0.875rem; opacity: 0.9;">点击开始体验数字人直播</div>
            </div>
        </div>
        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
    </body>
</html>