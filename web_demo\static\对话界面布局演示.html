<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 数字人对话界面布局演示</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            padding: var(--spacing-xl);
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            color: white;
        }

        .demo-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .demo-header p {
            font-size: 1.125rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .demo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .demo-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .demo-card h3 i {
            font-size: 1.75rem;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .feature-list li i {
            color: var(--success-color);
            font-size: 1rem;
        }

        /* 模拟对话界面 */
        .dialog-preview {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 500px;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .dialog-header {
            background: var(--primary-color);
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dialog-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding-bottom: 80px; /* 为底部输入区域预留空间 */
        }

        .dialog-top-area {
            height: 40%;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .dialog-chat-area {
            flex: 1;
            padding: var(--spacing-md);
            overflow-y: auto;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 100%);
        }

        .sample-message {
            margin-bottom: var(--spacing-sm);
            display: flex;
        }

        .sample-message.ai {
            justify-content: flex-start;
        }

        .sample-message.user {
            justify-content: flex-end;
        }

        .sample-message-content {
            max-width: 70%;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
        }

        .sample-message.ai .sample-message-content {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .sample-message.user .sample-message-content {
            background-color: var(--primary-color);
            color: white;
        }

        .dialog-input-area {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border-color);
            padding: var(--spacing-md);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .dialog-input-container {
            display: flex;
            align-items: center;
            background: var(--bg-secondary);
            border-radius: 25px;
            padding: var(--spacing-sm) var(--spacing-md);
            gap: var(--spacing-sm);
        }

        .dialog-input-container input {
            flex: 1;
            border: none;
            outline: none;
            background: none;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .dialog-input-container button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dialog-input-container button:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }

        .comparison-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: var(--spacing-xl);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            margin-top: var(--spacing-lg);
        }

        .comparison-item {
            text-align: center;
        }

        .comparison-item h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .before {
            color: var(--warning-color);
        }

        .after {
            color: var(--success-color);
        }

        .comparison-preview {
            width: 100%;
            height: 200px;
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 1.125rem;
            position: relative;
            overflow: hidden;
        }

        .before-preview {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .after-preview {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }

        .input-position-demo {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }

            .demo-grid {
                grid-template-columns: 1fr;
            }

            .comparison-grid {
                grid-template-columns: 1fr;
            }

            .dialog-preview {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示标题 -->
        <div class="demo-header">
            <h1>💬 对话界面布局优化演示</h1>
            <p>展示输入区域移动到页面底部后的布局改进效果</p>
        </div>

        <!-- 功能特性展示 -->
        <div class="demo-grid">
            <!-- 布局优化特性 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-layout"></i>
                    布局优化
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 输入区域固定在页面底部</li>
                    <li><i class="fas fa-check"></i> 滚动时输入区域保持可见</li>
                    <li><i class="fas fa-check"></i> 聊天内容区域自动调整高度</li>
                    <li><i class="fas fa-check"></i> 更符合现代聊天应用习惯</li>
                </ul>
            </div>

            <!-- 用户体验改进 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-user-check"></i>
                    用户体验
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 输入框始终可见和可访问</li>
                    <li><i class="fas fa-check"></i> 减少用户滚动操作</li>
                    <li><i class="fas fa-check"></i> 提升输入效率和便利性</li>
                    <li><i class="fas fa-check"></i> 更好的视觉层次结构</li>
                </ul>
            </div>

            <!-- 响应式设计 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-mobile-alt"></i>
                    响应式适配
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 桌面端完美显示</li>
                    <li><i class="fas fa-check"></i> 移动端优化布局</li>
                    <li><i class="fas fa-check"></i> 键盘弹出时自动适应</li>
                    <li><i class="fas fa-check"></i> 各种屏幕尺寸支持</li>
                </ul>
            </div>

            <!-- 技术实现 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-code"></i>
                    技术特性
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> CSS固定定位实现</li>
                    <li><i class="fas fa-check"></i> 毛玻璃效果增强视觉</li>
                    <li><i class="fas fa-check"></i> 平滑的过渡动画</li>
                    <li><i class="fas fa-check"></i> 高性能的布局方案</li>
                </ul>
            </div>
        </div>

        <!-- 对话界面预览 -->
        <div class="dialog-preview">
            <div class="dialog-header">
                <i class="fas fa-robot"></i>
                数字人对话界面
            </div>
            <div class="dialog-content">
                <div class="dialog-top-area">
                    <i class="fas fa-video"></i>
                    数字人视频显示区域
                </div>
                <div class="dialog-chat-area">
                    <div class="sample-message ai">
                        <div class="sample-message-content">
                            您好！我是您的AI助手，有什么可以帮助您的吗？
                        </div>
                    </div>
                    <div class="sample-message user">
                        <div class="sample-message-content">
                            你好，请介绍一下你的功能
                        </div>
                    </div>
                    <div class="sample-message ai">
                        <div class="sample-message-content">
                            我可以为您提供智能对话、问题解答、信息查询等多种服务。现在输入区域已经优化到页面底部，使用起来更加方便！
                        </div>
                    </div>
                </div>
            </div>
            <div class="dialog-input-area">
                <div class="dialog-input-container">
                    <input type="text" placeholder="输入您的消息..." />
                    <button>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 布局对比 -->
        <div class="comparison-section">
            <h2 style="text-align: center; font-size: 2rem; font-weight: 700; color: var(--primary-color); margin-bottom: var(--spacing-lg);">
                <i class="fas fa-balance-scale" style="margin-right: var(--spacing-sm);"></i>
                布局优化对比
            </h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4 class="before">
                        <i class="fas fa-times-circle"></i>
                        优化前
                    </h4>
                    <div class="comparison-preview before-preview">
                        传统布局
                        <div class="input-position-demo">
                            输入区域在内容流中
                        </div>
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i> 输入框可能被遮挡</li>
                        <li><i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i> 需要滚动才能输入</li>
                        <li><i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i> 用户体验不够流畅</li>
                        <li><i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i> 移动端使用不便</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h4 class="after">
                        <i class="fas fa-check-circle"></i>
                        优化后
                    </h4>
                    <div class="comparison-preview after-preview">
                        固定底部布局
                        <div class="input-position-demo">
                            输入区域固定在底部
                        </div>
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 输入框始终可见</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 无需滚动即可输入</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 用户体验更加流畅</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 移动端友好设计</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加页面加载动画
        window.addEventListener('load', () => {
            document.querySelectorAll('.demo-card, .dialog-preview').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });

        // 模拟输入框交互
        document.querySelector('.dialog-input-container input').addEventListener('focus', function() {
            this.parentElement.style.background = '#e8f4fd';
            this.parentElement.style.boxShadow = '0 0 0 2px rgba(99, 102, 241, 0.25)';
        });

        document.querySelector('.dialog-input-container input').addEventListener('blur', function() {
            this.parentElement.style.background = '#f9fafb';
            this.parentElement.style.boxShadow = 'none';
        });
    </script>
</body>
</html>
