<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📸 数字人直播系统 - 图片上传功能演示</title>
    <!-- 引入现代字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 使用与主页面相同的CSS变量 */
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            padding: var(--spacing-xl);
            transition: background 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            color: white;
        }

        .demo-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .demo-header p {
            font-size: 1.125rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: var(--spacing-xl);
            align-items: start;
        }

        /* 控制面板样式 - 复制自主页面 */
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: 
                var(--shadow-xl),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .panel-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .control-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .control-label i {
            color: var(--primary-color);
            font-size: 0.875rem;
        }

        /* 背景设置样式 */
        .background-controls {
            border-top: 2px solid var(--border-light);
            padding-top: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .background-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .background-options .background-option:nth-child(5) {
            grid-column: 1 / -1;
            max-width: calc(50% - var(--spacing-sm) / 2);
            margin: 0 auto;
        }

        .background-option {
            position: relative;
            width: 100%;
            height: 60px;
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 3px solid transparent;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .background-option:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: rgba(99, 102, 241, 0.3);
        }

        .background-option.active {
            border-color: var(--primary-color);
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px var(--primary-color);
            transform: translateY(-1px);
        }

        .background-preview {
            width: 100%;
            height: 100%;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
        }

        .background-check {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
        }

        .background-option.active .background-check {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .background-check i {
            color: var(--primary-color);
            font-size: 12px;
            font-weight: 700;
        }

        /* 渐变背景预设 */
        .bg-gradient-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-gradient-2 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .bg-gradient-3 {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .bg-gradient-4 {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }

        /* 图片上传选项样式 */
        .upload-option {
            position: relative;
            overflow: hidden;
        }

        .upload-preview {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            border: 2px dashed var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .upload-option:hover .upload-preview {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
        }

        .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            color: var(--text-secondary);
            font-size: 0.75rem;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-placeholder i {
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .upload-option:hover .upload-placeholder {
            color: var(--primary-color);
            transform: scale(1.05);
        }

        .upload-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            color: var(--primary-color);
            font-size: 0.75rem;
            font-weight: 500;
            text-align: center;
        }

        .upload-loading i {
            font-size: 1.25rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 10;
        }

        .custom-background-preview {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border: none !important;
        }

        .upload-option.drag-over .upload-preview {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
            transform: scale(1.02);
        }

        .upload-option.drag-over .upload-placeholder {
            color: var(--success-color);
            transform: scale(1.1);
        }

        .upload-error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--error-color);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
            z-index: 20;
            animation: fadeInOut 3s ease-in-out;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            10%, 90% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        /* 演示内容区域 */
        .demo-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-content h2 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .feature-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-light);
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .feature-card h3 i {
            color: var(--primary-color);
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .feature-list li i {
            color: var(--success-color);
            font-size: 0.875rem;
        }

        @media (max-width: 1024px) {
            .demo-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-lg);
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示标题 -->
        <div class="demo-header">
            <h1>📸 图片上传功能演示</h1>
            <p>体验全新的自定义背景图片上传功能，支持点击上传和拖拽上传两种方式</p>
        </div>

        <div class="demo-grid">
            <!-- 控制面板演示 -->
            <div class="control-panel">
                <div class="panel-title">
                    <i class="fas fa-robot"></i>
                    数字人直播控制台
                </div>

                <!-- 背景设置 -->
                <div class="control-group background-controls">
                    <div class="control-label">
                        <i class="fas fa-palette"></i>
                        背景设置
                    </div>
                    <div class="background-options">
                        <div class="background-option active" data-gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" title="蓝紫科技渐变">
                            <div class="background-preview bg-gradient-1"></div>
                            <div class="background-check">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="background-option" data-gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" title="青蓝科技渐变">
                            <div class="background-preview bg-gradient-2"></div>
                            <div class="background-check">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="background-option" data-gradient="linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)" title="紫粉科技渐变">
                            <div class="background-preview bg-gradient-3"></div>
                            <div class="background-check">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="background-option" data-gradient="linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)" title="深蓝科技渐变">
                            <div class="background-preview bg-gradient-4"></div>
                            <div class="background-check">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <!-- 自定义图片上传选项 -->
                        <div class="background-option upload-option" id="uploadOption" title="上传自定义背景图片">
                            <div class="background-preview upload-preview" id="uploadPreview">
                                <div class="upload-placeholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>上传图片</span>
                                </div>
                                <div class="upload-loading" id="uploadLoading" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>上传中...</span>
                                </div>
                            </div>
                            <div class="background-check">
                                <i class="fas fa-check"></i>
                            </div>
                            <input type="file" id="backgroundFileInput" class="file-input" accept="image/jpeg,image/jpg,image/png,image/gif,image/webp" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 演示内容 -->
            <div class="demo-content">
                <h2>
                    <i class="fas fa-camera"></i>
                    图片上传功能特性
                </h2>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>
                            <i class="fas fa-upload"></i>
                            多种上传方式
                        </h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> 点击上传按钮选择文件</li>
                            <li><i class="fas fa-check"></i> 拖拽图片到上传区域</li>
                            <li><i class="fas fa-check"></i> 支持重新上传替换</li>
                            <li><i class="fas fa-check"></i> 实时预览缩略图</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3>
                            <i class="fas fa-file-image"></i>
                            支持的格式
                        </h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> JPEG / JPG 格式</li>
                            <li><i class="fas fa-check"></i> PNG 格式（支持透明）</li>
                            <li><i class="fas fa-check"></i> GIF 格式（支持动画）</li>
                            <li><i class="fas fa-check"></i> WebP 格式（现代格式）</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3>
                            <i class="fas fa-shield-alt"></i>
                            安全限制
                        </h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> 文件大小限制 5MB</li>
                            <li><i class="fas fa-check"></i> 严格的格式验证</li>
                            <li><i class="fas fa-check"></i> 客户端处理，保护隐私</li>
                            <li><i class="fas fa-check"></i> 错误提示和处理</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3>
                            <i class="fas fa-magic"></i>
                            视觉效果
                        </h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> 自动适应屏幕尺寸</li>
                            <li><i class="fas fa-check"></i> 居中显示，覆盖模式</li>
                            <li><i class="fas fa-check"></i> 平滑的切换动画</li>
                            <li><i class="fas fa-check"></i> 保持界面清晰可读</li>
                        </ul>
                    </div>
                </div>

                <div style="margin-top: var(--spacing-xl); padding: var(--spacing-lg); background: var(--bg-secondary); border-radius: var(--radius-lg); border-left: 4px solid var(--primary-color);">
                    <h4 style="color: var(--primary-color); margin-bottom: var(--spacing-sm);">
                        <i class="fas fa-lightbulb"></i>
                        使用说明
                    </h4>
                    <ol style="color: var(--text-secondary); line-height: 1.8; padding-left: var(--spacing-lg);">
                        <li>点击左侧控制面板中的"上传图片"按钮</li>
                        <li>选择您想要的背景图片文件（支持JPG、PNG、GIF、WebP格式）</li>
                        <li>或者直接将图片拖拽到上传区域</li>
                        <li>上传成功后，图片将自动设置为背景并显示预览</li>
                        <li>可以随时点击其他渐变选项切换回预设背景</li>
                        <li>重新点击上传区域可以替换当前的自定义背景</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 背景切换管理器 - 包含图片上传功能
        class BackgroundManager {
            constructor() {
                this.currentBackground = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                this.customImageUrl = null;
                this.maxFileSize = 5 * 1024 * 1024; // 5MB
                this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                this.init();
            }

            init() {
                // 绑定背景选项点击事件
                const backgroundOptions = document.querySelectorAll('.background-option:not(.upload-option)');
                backgroundOptions.forEach(option => {
                    option.addEventListener('click', (e) => {
                        this.changeBackground(option);
                    });
                });

                // 设置初始状态
                this.setActiveOption(backgroundOptions[0]);

                // 初始化图片上传功能
                this.initImageUpload();
            }

            initImageUpload() {
                const uploadOption = document.getElementById('uploadOption');
                const fileInput = document.getElementById('backgroundFileInput');
                const uploadPreview = document.getElementById('uploadPreview');

                // 点击上传
                uploadOption.addEventListener('click', (e) => {
                    if (e.target === fileInput) return;
                    if (this.customImageUrl) {
                        this.changeToCustomBackground();
                    } else {
                        fileInput.click();
                    }
                });

                // 文件选择事件
                fileInput.addEventListener('change', (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        this.handleImageUpload(file);
                    }
                });

                // 拖拽上传
                uploadOption.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadOption.classList.add('drag-over');
                });

                uploadOption.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    uploadOption.classList.remove('drag-over');
                });

                uploadOption.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadOption.classList.remove('drag-over');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.handleImageUpload(files[0]);
                    }
                });
            }

            handleImageUpload(file) {
                // 验证文件类型
                if (!this.allowedTypes.includes(file.type)) {
                    this.showError('不支持的文件格式。请选择 JPG、PNG、GIF 或 WebP 格式的图片。');
                    return;
                }

                // 验证文件大小
                if (file.size > this.maxFileSize) {
                    this.showError('文件大小超过限制。请选择小于 5MB 的图片。');
                    return;
                }

                // 显示加载状态
                this.showUploadLoading(true);

                // 读取文件
                const reader = new FileReader();
                reader.onload = (e) => {
                    const imageUrl = e.target.result;
                    this.customImageUrl = imageUrl;

                    // 更新预览
                    this.updateUploadPreview(imageUrl);

                    // 切换到自定义背景
                    this.changeToCustomBackground();

                    // 隐藏加载状态
                    this.showUploadLoading(false);

                    console.log('Custom background image uploaded successfully');
                };

                reader.onerror = () => {
                    this.showError('图片读取失败，请重试。');
                    this.showUploadLoading(false);
                };

                reader.readAsDataURL(file);
            }

            changeToCustomBackground() {
                if (this.customImageUrl) {
                    // 设置自定义图片背景
                    document.body.style.background = `url(${this.customImageUrl})`;
                    document.body.style.backgroundSize = 'cover';
                    document.body.style.backgroundPosition = 'center';
                    document.body.style.backgroundRepeat = 'no-repeat';

                    this.currentBackground = this.customImageUrl;

                    // 设置上传选项为活跃状态
                    this.setActiveOption(document.getElementById('uploadOption'));

                    // 添加切换动画效果
                    this.addSwitchAnimation();
                }
            }

            changeBackground(selectedOption) {
                const gradient = selectedOption.dataset.gradient;

                if (gradient && gradient !== this.currentBackground) {
                    // 重置body样式为渐变背景
                    document.body.style.background = gradient;
                    document.body.style.backgroundSize = '';
                    document.body.style.backgroundPosition = '';
                    document.body.style.backgroundRepeat = '';

                    this.currentBackground = gradient;

                    // 更新活跃状态
                    this.setActiveOption(selectedOption);

                    // 添加切换动画效果
                    this.addSwitchAnimation();

                    console.log('Background changed to:', gradient);
                }
            }

            setActiveOption(selectedOption) {
                // 移除所有活跃状态
                const allOptions = document.querySelectorAll('.background-option');
                allOptions.forEach(option => {
                    option.classList.remove('active');
                });

                // 设置当前选中的活跃状态
                selectedOption.classList.add('active');
            }

            updateUploadPreview(imageUrl) {
                const uploadPreview = document.getElementById('uploadPreview');
                const placeholder = uploadPreview.querySelector('.upload-placeholder');

                // 设置背景图片
                uploadPreview.style.backgroundImage = `url(${imageUrl})`;
                uploadPreview.style.backgroundSize = 'cover';
                uploadPreview.style.backgroundPosition = 'center';
                uploadPreview.classList.add('custom-background-preview');

                // 隐藏占位符
                if (placeholder) {
                    placeholder.style.display = 'none';
                }
            }

            showUploadLoading(show) {
                const loadingElement = document.getElementById('uploadLoading');
                const placeholder = document.querySelector('.upload-placeholder');

                if (show) {
                    loadingElement.style.display = 'flex';
                    if (placeholder) placeholder.style.display = 'none';
                } else {
                    loadingElement.style.display = 'none';
                    if (placeholder && !this.customImageUrl) {
                        placeholder.style.display = 'flex';
                    }
                }
            }

            showError(message) {
                // 移除现有的错误提示
                const existingError = document.querySelector('.upload-error');
                if (existingError) {
                    existingError.remove();
                }

                // 创建新的错误提示
                const errorElement = document.createElement('div');
                errorElement.className = 'upload-error';
                errorElement.textContent = message;

                // 添加到上传选项中
                const uploadOption = document.getElementById('uploadOption');
                uploadOption.appendChild(errorElement);

                // 3秒后自动移除
                setTimeout(() => {
                    if (errorElement.parentNode) {
                        errorElement.remove();
                    }
                }, 3000);
            }

            addSwitchAnimation() {
                // 添加临时的切换动画类
                document.body.classList.add('background-switching');

                setTimeout(() => {
                    document.body.classList.remove('background-switching');
                }, 500);
            }
        }

        // 页面加载完成后初始化背景管理器
        document.addEventListener('DOMContentLoaded', () => {
            const backgroundManager = new BackgroundManager();
        });
    </script>
</body>
</html>
