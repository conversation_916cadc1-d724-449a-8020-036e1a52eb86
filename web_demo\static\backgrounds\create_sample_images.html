<!DOCTYPE html>
<html>
<head>
    <title>创建示例背景图片</title>
</head>
<body>
    <h1>创建示例背景图片</h1>
    <p>这个页面用于生成示例背景图片</p>
    
    <script>
        // 创建示例背景图片的函数
        function createSampleImage(name, width, height, color, text) {
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            // 绘制背景色
            ctx.fillStyle = color;
            ctx.fillRect(0, 0, width, height);
            
            // 添加文字
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, width/2, height/2);
            
            // 下载图片
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = name + '.jpg';
                a.click();
                URL.revokeObjectURL(url);
            }, 'image/jpeg', 0.8);
        }
        
        // 创建示例图片
        setTimeout(() => {
            createSampleImage('office', 1920, 1080, '#4a90e2', '办公室');
        }, 1000);
        
        setTimeout(() => {
            createSampleImage('studio', 1920, 1080, '#2c3e50', '演播室');
        }, 2000);
        
        setTimeout(() => {
            createSampleImage('living_room', 1920, 1080, '#e67e22', '客厅');
        }, 3000);
        
        setTimeout(() => {
            createSampleImage('conference', 1920, 1080, '#34495e', '会议室');
        }, 4000);
        
        setTimeout(() => {
            createSampleImage('nature', 1920, 1080, '#27ae60', '自然风景');
        }, 5000);
    </script>
</body>
</html>
