# 数字人直播系统 - 背景替换功能使用说明

## 功能概述

数字人直播系统现已支持完整的背景替换功能，包括预设背景选择、自定义背景上传、实时切换和平滑过渡效果。

## 主要功能

### 1. 预设背景选择
- **位置**: 页面左上角第三个下拉菜单（在角色和语音选择器下方）
- **选项**: 
  - 默认背景（纯色）
  - 办公室
  - 演播室
  - 客厅
  - 会议室
  - 自然风景
  - 蓝色渐变
  - 紫色渐变
  - 绿色渐变

### 2. 自定义背景上传
- **位置**: 背景选择器下方的"上传背景"按钮
- **支持格式**: JPG, JPEG, PNG, GIF, WebP
- **文件大小限制**: 最大5MB
- **推荐尺寸**: 1920x1080 (16:9比例)

### 3. 操作方式

#### 选择预设背景
1. 点击"背景选择"下拉菜单
2. 选择想要的背景选项
3. 背景会自动切换，带有平滑过渡效果

#### 上传自定义背景
1. 点击"上传背景"按钮
2. 选择图片文件（或直接拖拽图片到页面）
3. 系统会显示预览窗口
4. 点击"应用"确认使用，或点击"取消"放弃

#### 键盘快捷键
- **Ctrl+U** (或 Cmd+U): 快速打开文件上传对话框
- **Esc**: 取消当前预览

#### 拖拽上传
- 直接将图片文件拖拽到页面任意位置即可上传

## 技术特性

### 性能优化
- 背景渲染频率限制（约60FPS）
- 图片自动压缩和尺寸优化
- 内存使用优化

### 视觉效果
- 平滑过渡动画（300ms）
- 图片自适应缩放（保持宽高比）
- 渐变背景支持

### 错误处理
- 文件格式验证
- 文件大小检查
- 加载状态提示
- 错误信息显示

## 兼容性

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 移动设备
- iOS 12+ (Safari)
- Android 7+ (Chrome)

## 故障排除

### 常见问题

1. **背景不显示**
   - 检查图片文件是否损坏
   - 确认文件格式是否支持
   - 刷新页面重试

2. **上传失败**
   - 检查文件大小是否超过5MB
   - 确认文件格式是否正确
   - 检查网络连接

3. **性能问题**
   - 使用较小的图片文件
   - 关闭其他占用资源的程序
   - 更新浏览器到最新版本

### 调试工具
- 访问 `test_backgrounds.html` 进行功能测试
- 打开浏览器开发者工具查看控制台信息

## 开发者信息

### 文件结构
```
backgrounds/
├── backgrounds.json     # 背景配置文件
├── README.md           # 图片说明
├── 使用说明.md         # 本文件
├── test_backgrounds.html # 测试页面
└── [背景图片文件]
```

### 配置文件格式
```json
{
  "presets": [
    {
      "id": "背景ID",
      "name": "显示名称",
      "description": "描述",
      "type": "类型(solid/gradient/image)",
      "file": "图片文件名(可选)",
      "color": "颜色值(纯色背景)",
      "gradient": "CSS渐变(渐变背景)"
    }
  ],
  "settings": {
    "maxFileSize": 5242880,
    "allowedFormats": ["jpg", "jpeg", "png", "gif", "webp"],
    "recommendedSize": {
      "width": 1920,
      "height": 1080
    }
  }
}
```

## 更新日志

### v1.0.0 (2025-06-23)
- 初始版本发布
- 支持预设背景选择
- 支持自定义背景上传
- 实现平滑过渡效果
- 添加性能优化
- 支持拖拽上传
- 添加键盘快捷键
