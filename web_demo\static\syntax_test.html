<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语法测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>JavaScript语法测试</h1>
        <div id="testResult"></div>
        
        <h2>测试结果</h2>
        <div id="syntaxResult"></div>
    </div>

    <script>
        function testSyntax() {
            try {
                // 测试BackgroundManager类是否可以正常创建
                class TestBackgroundManager {
                    constructor() {
                        this.currentBackground = null;
                        this.backgroundImage = null;
                        this.backgroundType = 'solid';
                    }
                    
                    async changeBackground(backgroundId) {
                        console.log('Testing changeBackground method');
                        return true;
                    }
                    
                    showDropZone(show) {
                        if (show) {
                            document.body.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                        } else {
                            document.body.style.backgroundColor = '';
                        }
                    }
                }
                
                // 测试TransitionManager类
                class TestTransitionManager {
                    constructor() {
                        this.isTransitioning = false;
                        this.transitionDuration = 300;
                    }
                    
                    startTransition(oldBg, newBg) {
                        return true;
                    }
                    
                    easeInOutCubic(t) {
                        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
                    }
                }
                
                // 创建测试实例
                const testBgManager = new TestBackgroundManager();
                const testTransManager = new TestTransitionManager();
                
                // 测试方法调用
                testBgManager.showDropZone(true);
                testBgManager.showDropZone(false);
                testBgManager.changeBackground('test');
                
                testTransManager.startTransition(null, null);
                testTransManager.easeInOutCubic(0.5);
                
                document.getElementById('syntaxResult').innerHTML = 
                    '<div class="result success">✓ JavaScript语法测试通过！所有类和方法都可以正常创建和调用。</div>';
                
            } catch (error) {
                document.getElementById('syntaxResult').innerHTML = 
                    '<div class="result error">✗ JavaScript语法错误: ' + error.message + '</div>';
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            document.getElementById('testResult').innerHTML = 
                '<div class="result">正在测试JavaScript语法...</div>';
            
            setTimeout(testSyntax, 100);
        });
    </script>
</body>
</html>
