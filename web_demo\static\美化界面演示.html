<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 数字人直播系统 - 美化界面演示</title>
    <!-- 引入现代字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 使用与主页面相同的CSS变量 */
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            padding: var(--spacing-xl);
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            color: white;
        }

        .demo-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .demo-header p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .demo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .demo-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .demo-card h3 i {
            font-size: 1.75rem;
        }

        .feature-list {
            list-style: none;
            space-y: var(--spacing-sm);
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .feature-list li i {
            color: var(--success-color);
            font-size: 1rem;
        }

        .color-showcase {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .color-item {
            width: 100%;
            height: 3rem;
            border-radius: var(--radius-md);
            border: 2px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .color-item:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .gradient-showcase {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .gradient-item {
            height: 4rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .gradient-item:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
        }

        .button-showcase {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .demo-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
            color: white;
        }

        .btn-error {
            background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
            color: white;
        }

        .status-demo {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin: var(--spacing-sm) 0;
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-light);
        }

        .status-dot {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
        }

        .status-ready {
            background: var(--success-color);
            animation: pulse 2s ease-in-out infinite;
        }

        .status-loading {
            background: var(--warning-color);
            animation: spin 1s linear infinite;
        }

        .status-error {
            background: var(--error-color);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .comparison-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: var(--spacing-xl);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            margin-top: var(--spacing-lg);
        }

        .comparison-item {
            text-align: center;
        }

        .comparison-item h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .old-design {
            color: var(--error-color);
        }

        .new-design {
            color: var(--success-color);
        }

        .comparison-preview {
            width: 100%;
            height: 200px;
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
        }

        .old-preview {
            background: linear-gradient(135deg, #ccc 0%, #999 100%);
        }

        .new-preview {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }

            .demo-grid {
                grid-template-columns: 1fr;
            }

            .comparison-grid {
                grid-template-columns: 1fr;
            }

            .color-showcase {
                grid-template-columns: repeat(4, 1fr);
            }

            .gradient-showcase {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示标题 -->
        <div class="demo-header">
            <h1>🎨 界面美化升级展示</h1>
            <p>全新的现代化设计语言，为数字人直播系统带来专业级的视觉体验</p>
        </div>

        <!-- 功能展示网格 -->
        <div class="demo-grid">
            <!-- 设计系统 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-palette"></i>
                    现代设计系统
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 统一的色彩规范和变量系统</li>
                    <li><i class="fas fa-check"></i> 现代化的圆角和阴影设计</li>
                    <li><i class="fas fa-check"></i> 毛玻璃效果和背景模糊</li>
                    <li><i class="fas fa-check"></i> 流畅的动画和过渡效果</li>
                    <li><i class="fas fa-check"></i> 响应式布局适配</li>
                </ul>
            </div>

            <!-- 交互体验 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-mouse-pointer"></i>
                    交互体验升级
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 微交互动画反馈</li>
                    <li><i class="fas fa-check"></i> 悬停和焦点状态优化</li>
                    <li><i class="fas fa-check"></i> 平滑的状态过渡</li>
                    <li><i class="fas fa-check"></i> 直观的视觉层次</li>
                    <li><i class="fas fa-check"></i> 无障碍设计考虑</li>
                </ul>
            </div>

            <!-- 颜色系统 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-swatchbook"></i>
                    丰富色彩选择
                </h3>
                <div class="color-showcase">
                    <div class="color-item" style="background: #ffffff; border-color: #ccc;" title="纯白色"></div>
                    <div class="color-item" style="background: #000000;" title="纯黑色"></div>
                    <div class="color-item" style="background: #3b82f6;" title="天空蓝"></div>
                    <div class="color-item" style="background: #10b981;" title="翡翠绿"></div>
                    <div class="color-item" style="background: #ef4444;" title="珊瑚红"></div>
                    <div class="color-item" style="background: #f59e0b;" title="琥珀黄"></div>
                    <div class="color-item" style="background: #8b5cf6;" title="紫罗兰"></div>
                    <div class="color-item" style="background: #f97316;" title="橘子橙"></div>
                    <div class="color-item" style="background: #06b6d4;" title="青瓷蓝"></div>
                    <div class="color-item" style="background: #ec4899;" title="玫瑰粉"></div>
                    <div class="color-item" style="background: #6b7280;" title="石墨灰"></div>
                    <div class="color-item" style="background: #f3f4f6;" title="云雾白"></div>
                </div>
                <p style="font-size: 0.75rem; color: var(--text-light); text-align: center;">
                    12种精心挑选的预设颜色 + 自定义颜色选择器
                </p>
            </div>

            <!-- 渐变背景 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-fill-drip"></i>
                    渐变背景效果
                </h3>
                <div class="gradient-showcase">
                    <div class="gradient-item" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        蓝紫渐变
                    </div>
                    <div class="gradient-item" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        粉红渐变
                    </div>
                    <div class="gradient-item" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        青蓝渐变
                    </div>
                </div>
                <p style="font-size: 0.75rem; color: var(--text-light); text-align: center;">
                    专业级渐变效果，支持自定义CSS渐变
                </p>
            </div>

            <!-- 按钮系统 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-hand-pointer"></i>
                    按钮交互系统
                </h3>
                <div class="button-showcase">
                    <button class="demo-btn btn-primary">
                        <i class="fas fa-play"></i>
                        主要操作
                    </button>
                    <button class="demo-btn btn-success">
                        <i class="fas fa-check"></i>
                        确认操作
                    </button>
                    <button class="demo-btn btn-warning">
                        <i class="fas fa-exclamation"></i>
                        警告操作
                    </button>
                    <button class="demo-btn btn-error">
                        <i class="fas fa-times"></i>
                        危险操作
                    </button>
                </div>
                <p style="font-size: 0.75rem; color: var(--text-light); text-align: center;">
                    渐变按钮 + 图标 + 悬停动画
                </p>
            </div>

            <!-- 状态指示 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-signal"></i>
                    状态指示系统
                </h3>
                <div class="status-demo">
                    <div class="status-dot status-ready"></div>
                    <span>系统就绪</span>
                </div>
                <div class="status-demo">
                    <div class="status-dot status-loading"></div>
                    <span>正在加载...</span>
                </div>
                <div class="status-demo">
                    <div class="status-dot status-error"></div>
                    <span>发生错误</span>
                </div>
                <p style="font-size: 0.75rem; color: var(--text-light); text-align: center;">
                    动态状态指示器 + 实时反馈
                </p>
            </div>
        </div>

        <!-- 对比展示 -->
        <div class="comparison-section">
            <h2 style="text-align: center; font-size: 2rem; font-weight: 700; color: var(--primary-color); margin-bottom: var(--spacing-lg);">
                <i class="fas fa-before-after" style="margin-right: var(--spacing-sm);"></i>
                设计对比展示
            </h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4 class="old-design">
                        <i class="fas fa-times-circle"></i>
                        旧版设计
                    </h4>
                    <div class="comparison-preview old-preview">
                        基础样式界面
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-times" style="color: var(--error-color);"></i> 简单的边框和背景</li>
                        <li><i class="fas fa-times" style="color: var(--error-color);"></i> 缺乏视觉层次</li>
                        <li><i class="fas fa-times" style="color: var(--error-color);"></i> 静态交互体验</li>
                        <li><i class="fas fa-times" style="color: var(--error-color);"></i> 有限的颜色选择</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h4 class="new-design">
                        <i class="fas fa-check-circle"></i>
                        新版设计
                    </h4>
                    <div class="comparison-preview new-preview">
                        现代化美观界面
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 毛玻璃效果和渐变</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 清晰的视觉层次</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 流畅的动画交互</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 丰富的颜色系统</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.color-item').forEach(item => {
            item.addEventListener('click', function() {
                const color = this.style.backgroundColor;
                const title = this.getAttribute('title');
                alert(`选择了颜色: ${title}\nRGB值: ${color}`);
            });
        });

        document.querySelectorAll('.gradient-item').forEach(item => {
            item.addEventListener('click', function() {
                const text = this.textContent.trim();
                alert(`选择了渐变: ${text}`);
            });
        });

        document.querySelectorAll('.demo-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const text = this.textContent.trim();
                alert(`点击了按钮: ${text}`);
            });
        });

        // 添加页面加载动画
        window.addEventListener('load', () => {
            document.querySelectorAll('.demo-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
