<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #testCanvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>数字人直播系统 - 背景功能测试</h1>
        
        <div class="test-section">
            <h3>1. 配置文件测试</h3>
            <button onclick="testConfigFile()">测试配置文件加载</button>
            <div id="configResult"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 背景渲染测试</h3>
            <canvas id="testCanvas" width="400" height="300"></canvas>
            <br>
            <button onclick="testSolidBackground()">测试纯色背景</button>
            <button onclick="testGradientBackground()">测试渐变背景</button>
            <button onclick="testImageBackground()">测试图片背景</button>
            <div id="renderResult"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 文件验证测试</h3>
            <button onclick="testFileValidation()">测试文件格式验证</button>
            <button onclick="testFileSizeValidation()">测试文件大小验证</button>
            <div id="validationResult"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 性能测试</h3>
            <button onclick="testRenderPerformance()">测试渲染性能</button>
            <button onclick="testTransitionPerformance()">测试过渡效果性能</button>
            <div id="performanceResult"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 兼容性测试</h3>
            <button onclick="testBrowserCompatibility()">测试浏览器兼容性</button>
            <div id="compatibilityResult"></div>
        </div>
    </div>

    <script>
        // 模拟BackgroundManager类的简化版本用于测试
        class TestBackgroundManager {
            constructor() {
                this.backgroundConfig = null;
            }
            
            async loadBackgroundConfig() {
                try {
                    const response = await fetch('backgrounds/backgrounds.json');
                    this.backgroundConfig = await response.json();
                    return true;
                } catch (error) {
                    console.error('Config load error:', error);
                    return false;
                }
            }
            
            validateFileType(fileName) {
                const allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                const fileExtension = fileName.split('.').pop().toLowerCase();
                return allowedTypes.includes(fileExtension);
            }
            
            validateFileSize(fileSize) {
                const maxSize = 5242880; // 5MB
                return fileSize <= maxSize;
            }
            
            renderSolidBackground(ctx, width, height, color) {
                ctx.fillStyle = color;
                ctx.fillRect(0, 0, width, height);
            }
            
            renderGradientBackground(ctx, width, height) {
                const gradient = ctx.createLinearGradient(0, 0, width, height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, width, height);
            }
        }
        
        const testManager = new TestBackgroundManager();
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result test-${type}">${message}</div>`;
        }
        
        async function testConfigFile() {
            const success = await testManager.loadBackgroundConfig();
            if (success && testManager.backgroundConfig) {
                const presetCount = testManager.backgroundConfig.presets.length;
                showResult('configResult', 
                    `✓ 配置文件加载成功！找到 ${presetCount} 个预设背景`, 'pass');
            } else {
                showResult('configResult', 
                    '✗ 配置文件加载失败', 'fail');
            }
        }
        
        function testSolidBackground() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            try {
                testManager.renderSolidBackground(ctx, canvas.width, canvas.height, '#4CAF50');
                showResult('renderResult', '✓ 纯色背景渲染成功', 'pass');
            } catch (error) {
                showResult('renderResult', '✗ 纯色背景渲染失败: ' + error.message, 'fail');
            }
        }
        
        function testGradientBackground() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            try {
                testManager.renderGradientBackground(ctx, canvas.width, canvas.height);
                showResult('renderResult', '✓ 渐变背景渲染成功', 'pass');
            } catch (error) {
                showResult('renderResult', '✗ 渐变背景渲染失败: ' + error.message, 'fail');
            }
        }
        
        function testImageBackground() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // 创建测试图片
            const img = new Image();
            img.onload = function() {
                try {
                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                    showResult('renderResult', '✓ 图片背景渲染成功', 'pass');
                } catch (error) {
                    showResult('renderResult', '✗ 图片背景渲染失败: ' + error.message, 'fail');
                }
            };
            img.onerror = function() {
                showResult('renderResult', '✗ 测试图片加载失败', 'fail');
            };
            
            // 创建一个简单的测试图片
            const testCanvas = document.createElement('canvas');
            testCanvas.width = 100;
            testCanvas.height = 100;
            const testCtx = testCanvas.getContext('2d');
            testCtx.fillStyle = '#FF6B6B';
            testCtx.fillRect(0, 0, 100, 100);
            img.src = testCanvas.toDataURL();
        }
        
        function testFileValidation() {
            const validFiles = ['test.jpg', 'test.png', 'test.gif', 'test.webp'];
            const invalidFiles = ['test.txt', 'test.pdf', 'test.doc'];
            
            let results = [];
            
            validFiles.forEach(file => {
                const isValid = testManager.validateFileType(file);
                results.push(`${file}: ${isValid ? '✓' : '✗'}`);
            });
            
            invalidFiles.forEach(file => {
                const isValid = testManager.validateFileType(file);
                results.push(`${file}: ${!isValid ? '✓' : '✗'} (应该无效)`);
            });
            
            showResult('validationResult', 
                '文件格式验证结果:<br>' + results.join('<br>'), 'info');
        }
        
        function testFileSizeValidation() {
            const testSizes = [
                { size: 1024 * 1024, name: '1MB' },      // 有效
                { size: 3 * 1024 * 1024, name: '3MB' },  // 有效
                { size: 6 * 1024 * 1024, name: '6MB' },  // 无效
                { size: 10 * 1024 * 1024, name: '10MB' } // 无效
            ];
            
            let results = [];
            testSizes.forEach(test => {
                const isValid = testManager.validateFileSize(test.size);
                const expected = test.size <= 5 * 1024 * 1024;
                const correct = isValid === expected;
                results.push(`${test.name}: ${correct ? '✓' : '✗'} (${isValid ? '有效' : '无效'})`);
            });
            
            showResult('validationResult', 
                '文件大小验证结果:<br>' + results.join('<br>'), 'info');
        }
        
        function testRenderPerformance() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            const iterations = 100;
            
            const startTime = performance.now();
            
            for (let i = 0; i < iterations; i++) {
                testManager.renderSolidBackground(ctx, canvas.width, canvas.height, '#4CAF50');
            }
            
            const endTime = performance.now();
            const avgTime = (endTime - startTime) / iterations;
            
            showResult('performanceResult', 
                `渲染性能测试完成:<br>
                ${iterations} 次渲染耗时: ${(endTime - startTime).toFixed(2)}ms<br>
                平均每次渲染: ${avgTime.toFixed(2)}ms<br>
                预估FPS: ${(1000 / avgTime).toFixed(0)}`, 'info');
        }
        
        function testTransitionPerformance() {
            // 模拟过渡效果性能测试
            const startTime = performance.now();
            let frameCount = 0;
            
            function animationFrame() {
                frameCount++;
                if (frameCount < 60) { // 测试60帧
                    requestAnimationFrame(animationFrame);
                } else {
                    const endTime = performance.now();
                    const totalTime = endTime - startTime;
                    const fps = (frameCount / totalTime) * 1000;
                    
                    showResult('performanceResult', 
                        `过渡效果性能测试完成:<br>
                        ${frameCount} 帧耗时: ${totalTime.toFixed(2)}ms<br>
                        平均FPS: ${fps.toFixed(1)}`, 'info');
                }
            }
            
            requestAnimationFrame(animationFrame);
        }
        
        function testBrowserCompatibility() {
            const features = [
                { name: 'Canvas 2D', test: () => !!document.createElement('canvas').getContext('2d') },
                { name: 'File API', test: () => !!(window.File && window.FileReader && window.FileList && window.Blob) },
                { name: 'Fetch API', test: () => !!window.fetch },
                { name: 'Promise', test: () => !!window.Promise },
                { name: 'requestAnimationFrame', test: () => !!window.requestAnimationFrame },
                { name: 'CSS Gradients', test: () => {
                    const div = document.createElement('div');
                    div.style.background = 'linear-gradient(to right, red, blue)';
                    return div.style.background.includes('gradient');
                }}
            ];
            
            let results = [];
            features.forEach(feature => {
                try {
                    const supported = feature.test();
                    results.push(`${feature.name}: ${supported ? '✓ 支持' : '✗ 不支持'}`);
                } catch (error) {
                    results.push(`${feature.name}: ✗ 测试失败`);
                }
            });
            
            showResult('compatibilityResult', 
                '浏览器兼容性测试结果:<br>' + results.join('<br>'), 'info');
        }
        
        // 页面加载完成后自动运行一些基础测试
        window.addEventListener('load', () => {
            testConfigFile();
            testBrowserCompatibility();
        });
    </script>
</body>
</html>
