<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 数字人直播系统 - 简化界面演示</title>
    <!-- 引入现代字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 使用与主页面相同的CSS变量 */
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            padding: var(--spacing-xl);
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            color: white;
        }

        .demo-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .demo-header p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .demo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .demo-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .demo-card h3 i {
            font-size: 1.75rem;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .feature-list li i {
            color: var(--success-color);
            font-size: 1rem;
        }

        .control-panel-demo {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 380px;
            margin: 0 auto;
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .panel-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .control-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .control-label i {
            color: var(--primary-color);
            font-size: 0.875rem;
        }

        .dropdown-container {
            position: relative;
        }

        .dropdown-select {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 100%;
            padding: 0.875rem 3rem 0.875rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            border: 2px solid var(--border-light);
            border-radius: var(--radius-lg);
            cursor: pointer;
            outline: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-sm);
        }

        .dropdown-select:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
            background: var(--bg-primary);
        }

        .dropdown-select:focus {
            border-color: var(--primary-color);
            box-shadow: 
                var(--shadow-md),
                0 0 0 3px rgba(99, 102, 241, 0.1);
            transform: translateY(-1px);
        }

        .dropdown-container::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 1rem;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 6px solid var(--text-secondary);
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .dropdown-select:focus + .dropdown-container::after,
        .dropdown-container:hover::after {
            border-top-color: var(--primary-color);
            transform: translateY(-50%) rotate(180deg);
        }

        .comparison-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: var(--spacing-xl);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            margin-top: var(--spacing-lg);
        }

        .comparison-item {
            text-align: center;
        }

        .comparison-item h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .before {
            color: var(--warning-color);
        }

        .after {
            color: var(--success-color);
        }

        .comparison-preview {
            width: 100%;
            height: 200px;
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 1.125rem;
        }

        .before-preview {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .after-preview {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }

            .demo-grid {
                grid-template-columns: 1fr;
            }

            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示标题 -->
        <div class="demo-header">
            <h1>🎭 简化界面演示</h1>
            <p>移除背景设置功能后的清爽界面，专注于核心的数字人控制功能</p>
        </div>

        <!-- 功能展示网格 -->
        <div class="demo-grid">
            <!-- 简化后的功能 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-check-circle"></i>
                    保留的核心功能
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 数字人角色选择</li>
                    <li><i class="fas fa-check"></i> 语音类型选择</li>
                    <li><i class="fas fa-check"></i> 现代化界面设计</li>
                    <li><i class="fas fa-check"></i> 响应式布局</li>
                    <li><i class="fas fa-check"></i> 流畅的动画效果</li>
                    <li><i class="fas fa-check"></i> 毛玻璃视觉效果</li>
                </ul>
            </div>

            <!-- 移除的功能 -->
            <div class="demo-card">
                <h3>
                    <i class="fas fa-minus-circle"></i>
                    已移除的功能
                </h3>
                <ul class="feature-list">
                    <li><i class="fas fa-times" style="color: var(--error-color);"></i> 背景选择下拉菜单</li>
                    <li><i class="fas fa-times" style="color: var(--error-color);"></i> 颜色选择器</li>
                    <li><i class="fas fa-times" style="color: var(--error-color);"></i> 预设颜色面板</li>
                    <li><i class="fas fa-times" style="color: var(--error-color);"></i> 背景图片上传</li>
                    <li><i class="fas fa-times" style="color: var(--error-color);"></i> 拖拽上传功能</li>
                    <li><i class="fas fa-times" style="color: var(--error-color);"></i> 背景预览容器</li>
                    <li><i class="fas fa-times" style="color: var(--error-color);"></i> 状态指示器</li>
                </ul>
            </div>
        </div>

        <!-- 简化后的控制面板演示 -->
        <div class="control-panel-demo">
            <div class="panel-title">
                <i class="fas fa-robot"></i>
                数字人直播控制台
            </div>

            <!-- 角色选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-user"></i>
                    数字人角色
                </div>
                <div class="dropdown-container">
                    <select class="dropdown-select">
                        <option>🧑‍💼 商务女性</option>
                        <option>👩‍🎓 知性女性</option>
                        <option>👩‍💻 科技女性</option>
                        <option>👩‍🏫 教师女性</option>
                        <option>👩‍⚕️ 医护女性</option>
                    </select>
                </div>
            </div>

            <!-- 语音选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-microphone"></i>
                    语音类型
                </div>
                <div class="dropdown-container">
                    <select class="dropdown-select">
                        <option>🎵 温柔女声</option>
                        <option>🎙️ 温柔男声</option>
                        <option>🌸 甜美女声</option>
                        <option>✨ 青年女声</option>
                        <option>🎯 磁性男声</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 对比展示 -->
        <div class="comparison-section">
            <h2 style="text-align: center; font-size: 2rem; font-weight: 700; color: var(--primary-color); margin-bottom: var(--spacing-lg);">
                <i class="fas fa-balance-scale" style="margin-right: var(--spacing-sm);"></i>
                界面简化对比
            </h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4 class="before">
                        <i class="fas fa-cog"></i>
                        简化前
                    </h4>
                    <div class="comparison-preview before-preview">
                        复杂的背景设置界面
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-info-circle" style="color: var(--warning-color);"></i> 功能丰富但复杂</li>
                        <li><i class="fas fa-info-circle" style="color: var(--warning-color);"></i> 多个背景选择选项</li>
                        <li><i class="fas fa-info-circle" style="color: var(--warning-color);"></i> 颜色和上传功能</li>
                        <li><i class="fas fa-info-circle" style="color: var(--warning-color);"></i> 界面元素较多</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h4 class="after">
                        <i class="fas fa-check-circle"></i>
                        简化后
                    </h4>
                    <div class="comparison-preview after-preview">
                        简洁的核心控制界面
                    </div>
                    <ul class="feature-list">
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 专注核心功能</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 界面更加清爽</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 操作更加简单</li>
                        <li><i class="fas fa-check" style="color: var(--success-color);"></i> 减少用户困扰</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.dropdown-select').forEach(select => {
            select.addEventListener('change', function() {
                const value = this.value;
                alert(`选择了: ${value}`);
            });
        });

        // 添加页面加载动画
        window.addEventListener('load', () => {
            document.querySelectorAll('.demo-card, .control-panel-demo').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });
    </script>
</body>
</html>
