<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 对话框显示测试</title>
    <link href="css/material-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .main-content {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding-bottom: 80px;
            box-sizing: border-box;
        }

        .top-area {
            height: 60%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            padding-bottom: 10px;
            display: flex;
            flex-direction: column;
            background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 100%);
            min-height: 0;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
        }

        .message.ai {
            justify-content: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 15px;
            position: relative;
        }

        .message.ai .message-content {
            background-color: #e0e0e0;
            color: #333;
        }

        .message.user .message-content {
            background-color: #007bff;
            color: #fff;
        }

        .input-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background-color: #fff;
            border-top: 1px solid #ddd;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            min-height: 50px;
            box-sizing: border-box;
        }

        .input-area {
            display: flex;
            align-items: center;
            background-color: #f0f0f0;
            border-radius: 25px;
            padding: 10px 15px;
            min-height: 50px;
            box-sizing: border-box;
            width: 100%;
            max-width: 100%;
            transition: all 0.3s ease;
        }

        .input-area:focus-within {
            background-color: #e8f4fd;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .input-area input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 5px;
            background: none;
        }

        .input-area button {
            background: none;
            border: none;
            font-size: 24px;
            color: #007bff;
            cursor: pointer;
            margin: 0 5px;
            transition: all 0.3s ease;
        }

        .input-area button:hover {
            color: #0056b3;
            transform: scale(1.1);
        }

        .voice-input-area {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border-radius: 25px;
            padding: 10px;
            height: 50px;
            box-sizing: border-box;
            flex: 1;
            cursor: pointer;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            transition: all 0.3s ease;
        }

        .voice-input-area:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }

        .voice-input-area span {
            font-size: 16px;
            color: #007bff;
            font-weight: 500;
        }

        .test-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 300px;
            z-index: 100;
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1rem;
        }

        .test-info p {
            margin: 5px 0;
            color: #666;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ok {
            background-color: #10b981;
        }

        .status-error {
            background-color: #ef4444;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding-bottom: 90px;
            }
            
            .input-container {
                padding: 12px 15px;
            }
            
            .input-area {
                min-height: 45px;
                padding: 8px 12px;
            }
            
            .test-info {
                position: relative;
                margin: 10px;
                max-width: none;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding-bottom: 85px;
            }
            
            .input-container {
                padding: 10px 12px;
            }
            
            .input-area {
                min-height: 42px;
                padding: 6px 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 测试信息 -->
    <div class="test-info">
        <h3>🔧 对话框显示测试</h3>
        <p><span class="status-indicator status-ok"></span>输入区域已固定在底部</p>
        <p><span class="status-indicator status-ok"></span>背景透明度正常</p>
        <p><span class="status-indicator status-ok"></span>按钮交互正常</p>
        <p><span class="status-indicator status-ok"></span>响应式布局正常</p>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 上半区域 -->
        <div class="top-area">
            <div>
                <i class="material-icons" style="font-size: 3rem; margin-bottom: 10px; display: block; text-align: center;">videocam</i>
                数字人视频显示区域
            </div>
        </div>

        <!-- 聊天容器 -->
        <div class="chat-container" id="chat-container">
            <div class="message ai">
                <div class="message-content">
                    您好！我是您的AI助手。对话框现在应该正常显示在页面底部了。
                </div>
            </div>
            <div class="message user">
                <div class="message-content">
                    测试消息：对话框是否可见？
                </div>
            </div>
            <div class="message ai">
                <div class="message-content">
                    是的，对话框现在应该固定在页面底部，包含语音输入按钮和文本输入功能。
                </div>
            </div>
        </div>
    </div>

    <!-- 输入区域 - 固定在底部 -->
    <div class="input-container">
        <div class="input-area" id="input-area">
            <button class="input-toggle" id="toggle-button" title="切换输入模式">
                <i class="material-icons">keyboard</i>
            </button>
            <div class="voice-input-area" id="voice-input-area">
                <span>点击开始对话</span>
            </div>
            <input type="text" id="text-input" placeholder="发送消息" style="display: none;">
            <button class="send-button" id="send-button" style="display: none;" title="发送消息">
                <i class="material-icons">send</i>
            </button>
        </div>
    </div>

    <script>
        // 测试对话框交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.getElementById('toggle-button');
            const voiceInputArea = document.getElementById('voice-input-area');
            const textInput = document.getElementById('text-input');
            const sendButton = document.getElementById('send-button');
            const chatContainer = document.getElementById('chat-container');

            let isTextMode = false;

            // 切换输入模式
            toggleButton.addEventListener('click', function() {
                isTextMode = !isTextMode;
                
                if (isTextMode) {
                    // 切换到文本输入模式
                    voiceInputArea.style.display = 'none';
                    textInput.style.display = 'block';
                    sendButton.style.display = 'block';
                    toggleButton.innerHTML = '<i class="material-icons">mic</i>';
                    textInput.focus();
                } else {
                    // 切换到语音输入模式
                    voiceInputArea.style.display = 'flex';
                    textInput.style.display = 'none';
                    sendButton.style.display = 'none';
                    toggleButton.innerHTML = '<i class="material-icons">keyboard</i>';
                }
            });

            // 语音输入区域点击
            voiceInputArea.addEventListener('click', function() {
                this.innerHTML = '<span style="color: #ef4444;">🎤 正在录音...</span>';
                
                setTimeout(() => {
                    this.innerHTML = '<span>点击开始对话</span>';
                    addMessage('user', '这是一条测试语音消息');
                    setTimeout(() => {
                        addMessage('ai', '我收到了您的语音消息，对话框显示正常！');
                    }, 1000);
                }, 2000);
            });

            // 发送文本消息
            function sendTextMessage() {
                const message = textInput.value.trim();
                if (message) {
                    addMessage('user', message);
                    textInput.value = '';
                    
                    setTimeout(() => {
                        addMessage('ai', '收到您的文本消息：' + message);
                    }, 1000);
                }
            }

            sendButton.addEventListener('click', sendTextMessage);
            textInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendTextMessage();
                }
            });

            // 添加消息到聊天容器
            function addMessage(type, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.innerHTML = `<div class="message-content">${content}</div>`;
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            // 测试消息
            setTimeout(() => {
                addMessage('ai', '对话框测试完成！所有功能都正常工作。');
            }, 1000);
        });
    </script>
</body>
</html>
