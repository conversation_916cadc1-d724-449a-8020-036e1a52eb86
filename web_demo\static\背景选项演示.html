<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 数字人直播系统 - 背景选项演示</title>
    <!-- 引入现代字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 使用与主页面相同的CSS变量 */
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            padding: var(--spacing-xl);
        }

        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .demo-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            color: var(--primary-color);
        }

        .demo-header p {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .background-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .background-option {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .background-option:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .background-preview {
            width: 100%;
            height: 200px;
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .control-panel-demo {
            position: absolute;
            top: var(--spacing-sm);
            left: var(--spacing-sm);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.75rem;
            color: var(--text-primary);
            max-width: 120px;
        }

        .background-info h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .background-info p {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .background-code {
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.75rem;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            overflow-x: auto;
        }

        .copy-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: 0.75rem;
            cursor: pointer;
            margin-top: var(--spacing-xs);
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: var(--primary-dark);
        }

        .current-badge {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            background: var(--success-color);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* 背景预览样式 */
        .bg-slate-800 { background: #1e293b; }
        .bg-gray-700 { background: #374151; }
        .bg-white { background: #ffffff; color: #1f2937; }
        .bg-gradient-1 { background: linear-gradient(135deg, #1e293b 0%, #334155 100%); }
        .bg-gradient-2 { background: linear-gradient(135deg, #111827 0%, #374151 100%); }
        .bg-gradient-3 { background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%); }
        .bg-gradient-4 { background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%); }

        .text-white { color: #ffffff; }
        .text-dark { color: #1f2937; }

        @media (max-width: 768px) {
            .background-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示标题 -->
        <div class="demo-header">
            <h1>🎨 数字人直播系统背景选项</h1>
            <p>以下是可用的背景选项，点击复制按钮获取CSS代码，然后替换到MiniLive_RealTime.html中</p>
        </div>

        <!-- 背景选项网格 -->
        <div class="background-grid">
            <!-- 选项1: 深蓝色专业背景 (当前使用) -->
            <div class="background-option">
                <div class="background-preview bg-slate-800 text-white">
                    <div class="current-badge">当前使用</div>
                    <div class="control-panel-demo">
                        <div style="font-weight: 600; margin-bottom: 4px;">🤖 控制台</div>
                        <div>👤 数字人角色</div>
                        <div>🎤 语音类型</div>
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-robot" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600;">深蓝色专业背景</div>
                    </div>
                </div>
                <div class="background-info">
                    <h3><i class="fas fa-briefcase"></i> 深蓝色专业背景</h3>
                    <p>适合商务和专业场景，提供稳重可靠的视觉感受</p>
                    <div class="background-code">background: #1e293b;</div>
                    <button class="copy-btn" onclick="copyToClipboard('#1e293b')">复制代码</button>
                </div>
            </div>

            <!-- 选项2: 深灰色现代背景 -->
            <div class="background-option">
                <div class="background-preview bg-gray-700 text-white">
                    <div class="control-panel-demo">
                        <div style="font-weight: 600; margin-bottom: 4px;">🤖 控制台</div>
                        <div>👤 数字人角色</div>
                        <div>🎤 语音类型</div>
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-desktop" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600;">深灰色现代背景</div>
                    </div>
                </div>
                <div class="background-info">
                    <h3><i class="fas fa-laptop"></i> 深灰色现代背景</h3>
                    <p>现代化设计风格，适合科技和创新主题</p>
                    <div class="background-code">background: #374151;</div>
                    <button class="copy-btn" onclick="copyToClipboard('#374151')">复制代码</button>
                </div>
            </div>

            <!-- 选项3: 纯白色简洁背景 -->
            <div class="background-option">
                <div class="background-preview bg-white text-dark">
                    <div class="control-panel-demo" style="background: rgba(0, 0, 0, 0.05); color: #1f2937;">
                        <div style="font-weight: 600; margin-bottom: 4px;">🤖 控制台</div>
                        <div>👤 数字人角色</div>
                        <div>🎤 语音类型</div>
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-sun" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600;">纯白色简洁背景</div>
                    </div>
                </div>
                <div class="background-info">
                    <h3><i class="fas fa-circle" style="color: #ffffff; text-shadow: 0 0 2px #ccc;"></i> 纯白色简洁背景</h3>
                    <p>简洁明亮，适合教育和医疗等需要清晰视觉的场景</p>
                    <div class="background-code">background: #ffffff;</div>
                    <button class="copy-btn" onclick="copyToClipboard('#ffffff')">复制代码</button>
                </div>
            </div>

            <!-- 选项4: 静态蓝紫渐变背景 -->
            <div class="background-option">
                <div class="background-preview bg-gradient-1 text-white">
                    <div class="control-panel-demo">
                        <div style="font-weight: 600; margin-bottom: 4px;">🤖 控制台</div>
                        <div>👤 数字人角色</div>
                        <div>🎤 语音类型</div>
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-palette" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600;">蓝紫渐变背景</div>
                    </div>
                </div>
                <div class="background-info">
                    <h3><i class="fas fa-fill-drip"></i> 静态蓝紫渐变背景</h3>
                    <p>优雅的渐变效果，增加视觉层次感</p>
                    <div class="background-code">background: linear-gradient(135deg, #1e293b 0%, #334155 100%);</div>
                    <button class="copy-btn" onclick="copyToClipboard('linear-gradient(135deg, #1e293b 0%, #334155 100%)')">复制代码</button>
                </div>
            </div>

            <!-- 选项5: 静态深色渐变背景 -->
            <div class="background-option">
                <div class="background-preview bg-gradient-2 text-white">
                    <div class="control-panel-demo">
                        <div style="font-weight: 600; margin-bottom: 4px;">🤖 控制台</div>
                        <div>👤 数字人角色</div>
                        <div>🎤 语音类型</div>
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-moon" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600;">深色渐变背景</div>
                    </div>
                </div>
                <div class="background-info">
                    <h3><i class="fas fa-adjust"></i> 静态深色渐变背景</h3>
                    <p>深色主题，减少眼部疲劳，适合长时间使用</p>
                    <div class="background-code">background: linear-gradient(135deg, #111827 0%, #374151 100%);</div>
                    <button class="copy-btn" onclick="copyToClipboard('linear-gradient(135deg, #111827 0%, #374151 100%)')">复制代码</button>
                </div>
            </div>

            <!-- 选项6: 静态蓝灰渐变背景 -->
            <div class="background-option">
                <div class="background-preview bg-gradient-3 text-white">
                    <div class="control-panel-demo">
                        <div style="font-weight: 600; margin-bottom: 4px;">🤖 控制台</div>
                        <div>👤 数字人角色</div>
                        <div>🎤 语音类型</div>
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-water" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600;">蓝灰渐变背景</div>
                    </div>
                </div>
                <div class="background-info">
                    <h3><i class="fas fa-layer-group"></i> 静态蓝灰渐变背景</h3>
                    <p>三色渐变，丰富的视觉层次，适合创意场景</p>
                    <div class="background-code">background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);</div>
                    <button class="copy-btn" onclick="copyToClipboard('linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)')">复制代码</button>
                </div>
            </div>

            <!-- 选项7: 静态紫色渐变背景 -->
            <div class="background-option">
                <div class="background-preview bg-gradient-4 text-white">
                    <div class="control-panel-demo">
                        <div style="font-weight: 600; margin-bottom: 4px;">🤖 控制台</div>
                        <div>👤 数字人角色</div>
                        <div>🎤 语音类型</div>
                    </div>
                    <div style="text-align: center;">
                        <i class="fas fa-gem" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                        <div style="font-weight: 600;">紫色渐变背景</div>
                    </div>
                </div>
                <div class="background-info">
                    <h3><i class="fas fa-star"></i> 静态紫色渐变背景</h3>
                    <p>神秘优雅的紫色调，适合艺术和设计主题</p>
                    <div class="background-code">background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);</div>
                    <button class="copy-btn" onclick="copyToClipboard('linear-gradient(135deg, #1e1b4b 0%, #312e81 100%)')">复制代码</button>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div style="background: var(--bg-primary); border-radius: var(--radius-xl); padding: var(--spacing-xl); box-shadow: var(--shadow-lg); border: 1px solid var(--border-color);">
            <h2 style="color: var(--primary-color); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm);">
                <i class="fas fa-info-circle"></i>
                使用说明
            </h2>
            <ol style="color: var(--text-secondary); line-height: 1.8;">
                <li>选择您喜欢的背景选项</li>
                <li>点击"复制代码"按钮复制CSS代码</li>
                <li>打开 <code style="background: var(--bg-tertiary); padding: 2px 6px; border-radius: 4px;">MiniLive_RealTime.html</code> 文件</li>
                <li>找到body选择器中的background属性</li>
                <li>将复制的代码替换现有的background值</li>
                <li>保存文件并刷新页面查看效果</li>
            </ol>
            <div style="margin-top: var(--spacing-md); padding: var(--spacing-md); background: var(--bg-secondary); border-radius: var(--radius-md); border-left: 4px solid var(--primary-color);">
                <strong style="color: var(--primary-color);">💡 提示：</strong>
                <span style="color: var(--text-secondary);">如果选择白色背景，建议同时调整文字颜色以确保良好的对比度。</span>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // 创建临时提示
                const toast = document.createElement('div');
                toast.textContent = '✅ 代码已复制到剪贴板';
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #10b981;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-weight: 600;
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                `;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            }).catch(function(err) {
                alert('复制失败，请手动复制代码');
            });
        }

        // 添加滑入动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateX(100px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
