<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 数字人直播系统 - 背景设置功能演示</title>
    <!-- 引入现代字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 使用与主页面相同的CSS变量 */
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            padding: var(--spacing-xl);
            transition: background 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: var(--spacing-xl);
            align-items: start;
        }

        /* 控制面板样式 - 复制自主页面 */
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: 
                var(--shadow-xl),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .panel-title i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .control-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .control-label i {
            color: var(--primary-color);
            font-size: 0.875rem;
        }

        /* 背景设置样式 */
        .background-controls {
            border-top: 2px solid var(--border-light);
            padding-top: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .background-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .background-option {
            position: relative;
            width: 100%;
            height: 60px;
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 3px solid transparent;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .background-option:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: rgba(99, 102, 241, 0.3);
        }

        .background-option.active {
            border-color: var(--primary-color);
            box-shadow: 
                var(--shadow-md),
                0 0 0 1px var(--primary-color);
            transform: translateY(-1px);
        }

        .background-preview {
            width: 100%;
            height: 100%;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
        }

        .background-check {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
        }

        .background-option.active .background-check {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .background-check i {
            color: var(--primary-color);
            font-size: 12px;
            font-weight: 700;
        }

        /* 渐变背景预设 */
        .bg-gradient-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-gradient-2 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .bg-gradient-3 {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .bg-gradient-4 {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }

        /* 演示内容区域 */
        .demo-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-content h2 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .feature-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-light);
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .feature-card h3 i {
            color: var(--primary-color);
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .feature-list li i {
            color: var(--success-color);
            font-size: 0.875rem;
        }

        .gradient-showcase {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .gradient-item {
            height: 80px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            box-shadow: var(--shadow-md);
        }

        @media (max-width: 1024px) {
            .demo-container {
                grid-template-columns: 1fr;
                gap: var(--spacing-lg);
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .gradient-showcase {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 控制面板演示 -->
        <div class="control-panel">
            <div class="panel-title">
                <i class="fas fa-robot"></i>
                数字人直播控制台
            </div>

            <!-- 角色选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-user"></i>
                    数字人角色
                </div>
                <div style="padding: 0.875rem 1rem; background: var(--bg-secondary); border-radius: var(--radius-lg); border: 2px solid var(--border-light); color: var(--text-secondary);">
                    🧑‍💼 商务女性 (演示)
                </div>
            </div>

            <!-- 语音选择 -->
            <div class="control-group">
                <div class="control-label">
                    <i class="fas fa-microphone"></i>
                    语音类型
                </div>
                <div style="padding: 0.875rem 1rem; background: var(--bg-secondary); border-radius: var(--radius-lg); border: 2px solid var(--border-light); color: var(--text-secondary);">
                    🎵 温柔女声 (演示)
                </div>
            </div>

            <!-- 背景设置 -->
            <div class="control-group background-controls">
                <div class="control-label">
                    <i class="fas fa-palette"></i>
                    背景设置
                </div>
                <div class="background-options">
                    <div class="background-option active" data-gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" title="蓝紫科技渐变">
                        <div class="background-preview bg-gradient-1"></div>
                        <div class="background-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="background-option" data-gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" title="青蓝科技渐变">
                        <div class="background-preview bg-gradient-2"></div>
                        <div class="background-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="background-option" data-gradient="linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)" title="紫粉科技渐变">
                        <div class="background-preview bg-gradient-3"></div>
                        <div class="background-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="background-option" data-gradient="linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)" title="深蓝科技渐变">
                        <div class="background-preview bg-gradient-4"></div>
                        <div class="background-check">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 演示内容 -->
        <div class="demo-content">
            <h2>
                <i class="fas fa-magic"></i>
                背景设置功能演示
            </h2>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>
                        <i class="fas fa-palette"></i>
                        科技感渐变背景
                    </h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 4种精心设计的科技感渐变</li>
                        <li><i class="fas fa-check"></i> 现代化的视觉效果</li>
                        <li><i class="fas fa-check"></i> 与控制面板完美搭配</li>
                        <li><i class="fas fa-check"></i> 保持文字清晰可读</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>
                        <i class="fas fa-mouse-pointer"></i>
                        直观的交互设计
                    </h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 点击颜色方块即可切换</li>
                        <li><i class="fas fa-check"></i> 当前选中状态清晰指示</li>
                        <li><i class="fas fa-check"></i> 悬停效果增强体验</li>
                        <li><i class="fas fa-check"></i> 平滑的切换动画</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>
                        <i class="fas fa-mobile-alt"></i>
                        响应式设计
                    </h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 桌面端完美显示</li>
                        <li><i class="fas fa-check"></i> 移动端友好适配</li>
                        <li><i class="fas fa-check"></i> 触摸操作优化</li>
                        <li><i class="fas fa-check"></i> 各种屏幕尺寸支持</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>
                        <i class="fas fa-cogs"></i>
                        技术特性
                    </h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 不影响数字人渲染</li>
                        <li><i class="fas fa-check"></i> 平滑的CSS过渡动画</li>
                        <li><i class="fas fa-check"></i> 高性能的实现方式</li>
                        <li><i class="fas fa-check"></i> 现代浏览器兼容</li>
                    </ul>
                </div>
            </div>

            <h3 style="color: var(--primary-color); margin-bottom: var(--spacing-md);">
                <i class="fas fa-swatchbook"></i>
                可选背景渐变
            </h3>
            <div class="gradient-showcase">
                <div class="gradient-item bg-gradient-1">蓝紫科技渐变</div>
                <div class="gradient-item bg-gradient-2">青蓝科技渐变</div>
                <div class="gradient-item bg-gradient-3">紫粉科技渐变</div>
                <div class="gradient-item bg-gradient-4">深蓝科技渐变</div>
            </div>

            <div style="margin-top: var(--spacing-xl); padding: var(--spacing-lg); background: var(--bg-secondary); border-radius: var(--radius-lg); border-left: 4px solid var(--primary-color);">
                <h4 style="color: var(--primary-color); margin-bottom: var(--spacing-sm);">
                    <i class="fas fa-lightbulb"></i>
                    使用说明
                </h4>
                <p style="color: var(--text-secondary); line-height: 1.6;">
                    点击左侧控制面板中的任意颜色方块即可切换背景。每个方块都显示了对应的渐变效果预览，
                    当前选中的背景会显示勾选标记。背景切换采用了平滑的过渡动画，
                    确保视觉体验的连贯性和专业感。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 背景切换功能 - 与主页面相同的实现
        class BackgroundManager {
            constructor() {
                this.currentBackground = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                this.init();
            }

            init() {
                const backgroundOptions = document.querySelectorAll('.background-option');
                backgroundOptions.forEach(option => {
                    option.addEventListener('click', (e) => {
                        this.changeBackground(option);
                    });
                });
                this.setActiveOption(backgroundOptions[0]);
            }

            changeBackground(selectedOption) {
                const gradient = selectedOption.dataset.gradient;
                
                if (gradient && gradient !== this.currentBackground) {
                    document.body.style.background = gradient;
                    this.currentBackground = gradient;
                    this.setActiveOption(selectedOption);
                    this.addSwitchAnimation();
                    console.log('Background changed to:', gradient);
                }
            }

            setActiveOption(selectedOption) {
                const allOptions = document.querySelectorAll('.background-option');
                allOptions.forEach(option => {
                    option.classList.remove('active');
                });
                selectedOption.classList.add('active');
            }

            addSwitchAnimation() {
                document.body.classList.add('background-switching');
                setTimeout(() => {
                    document.body.classList.remove('background-switching');
                }, 500);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            const backgroundManager = new BackgroundManager();
        });
    </script>
</body>
</html>
